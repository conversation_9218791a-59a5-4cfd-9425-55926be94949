const express = require('express');
const bodyParser = require('body-parser');
const fetch = require('node-fetch');
const AutoPublishPlugin = require('./main');
const cors = require('cors');
const fs = require('fs');
const path = require('path');

// 统一管理远程API端点
const REMOTE_API_ENDPOINT = 'http://api.274500.xyz/api/task';

/**
 * API服务类 - 整合API接收器和HTTP API服务
 */
class ApiService {
  constructor(plugin) {
    this.plugin = AutoPublishPlugin;
    this.logger = this.plugin.logger || console;
    this.app = express();
    this.apiPort = this.plugin.config?.apiPort || 3000;
    this.apiToken = this.plugin.config?.apiToken || this.generateToken();
  }

  /**
   * 初始化API服务
   */
  async init() {
    try {
      // 配置Express中间件
      this.setupMiddlewares();
      
      // 注册API路由
      this.registerRoutes();
      
      // 启动HTTP服务器
      this.startServer();
      
      this.logger.log(`API服务初始化成功，令牌: ${this.apiToken}`);
      return true;
    } catch (error) {
      this.logger.error('API服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 配置Express中间件
   */
  setupMiddlewares() {
    this.app.use(bodyParser.json());
    this.app.use(cors()); // 启用CORS支持
    this.app.use(express.urlencoded({ extended: true }));
    
    // 请求日志中间件
    this.app.use((req, res, next) => {
      this.logger.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
      next();
    });
  }

  /**
   * 注册API路由
   */
  registerRoutes() {
    // 登录接口
    this.app.post('/api/token', this.handleGetToken.bind(this));
    
    // 获取平台列表
    this.app.get('/api/platforms', this.handleGetPlatforms.bind(this));
    
    // 发布内容（本地业务）
    this.app.post('/api/task', this.handleTaskRequest.bind(this));
    
    // 代理转发到远程API
    this.app.post('/api/remote-task', this.handleRemoteTask.bind(this));
    
    // 404处理
    this.app.use(this.handle404.bind(this));
    
    // 错误处理中间件
    this.app.use(this.handleError.bind(this));
  }

  /**
   * 启动HTTP服务器
   */
  startServer() {
    this.server = this.app.listen(this.apiPort, () => {
      this.logger.log(`API服务已启动，端口: ${this.apiPort}`);
    });
  }

  /**
   * 处理获取API令牌请求
   */
  async handleGetToken(req, res, next) {
    const { username, password } = req.body;
    try {
      if (!username || !password) {
        return res.status(400).json({ success: false, error: '帐号或密码不能为空' });
      }
      // 这里应有真实的账号密码校验逻辑
      // 假设通过校验
      res.json({ success: true, token: this.apiToken });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 处理获取平台列表请求
   */
  async handleGetPlatforms(req, res, next) {
    try {
      // 获取支持的平台列表
      const platforms = this.plugin.getPlatforms ? 
        this.plugin.getPlatforms() : 
        Array.from(this.plugin.platformAdapters?.keys() || []);
      
      res.json({ success: true, platforms });
    } catch (error) {
      next(error);
    }
  }

  /**
   * 处理任务请求
   */
  async handleTaskRequest(req, res, next) {
    try {
      const taskData = req.body;
      
      // 验证任务数据
      this.validateTaskData(taskData);
      
      // 认证令牌校验（可扩展）
      // const token = req.headers['authorization']?.replace('Bearer ', '');
      // if (token !== this.apiToken) {
      //   return res.status(401).json({ success: false, error: '无效的API令牌' });
      // }
      
      // 调用发布内容方法
      const result = await this.plugin.publishContent(taskData);
      
      res.json({
        success: result.success,
        task_id: result.taskId,
        message: result.message || '任务已提交'
      });
    } catch (error) {
      // 如果是业务逻辑错误，返回400
      if (error.message.includes('不能为空') || error.message.includes('不支持的平台')) {
        return res.status(400).json({
          success: false,
          error: error.message
        });
      }
      next(error);
    }
  }

  /**
   * 处理远程任务请求
   */
  async handleRemoteTask(req, res, next) {
    try {
      const response = await fetch(REMOTE_API_ENDPOINT, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(req.body)
      });
      const data = await response.json();
      res.json(data);
    } catch (error) {
      next(error);
    }
  }

  /**
   * 处理404错误
   */
  handle404(req, res) {
    res.status(404).json({
      success: false,
      error: '未找到请求的API端点'
    });
  }

  /**
   * 处理API错误
   */
  handleError(err, req, res, next) {
    this.logger.error('API错误:', err);
    res.status(500).json({
      success: false,
      error: err.message || '服务器内部错误'
    });
  }

  /**
   * 验证任务数据
   * @param {Object} taskData 任务数据
   * @throws {Error} 验证失败时抛出错误
   */
  validateTaskData(taskData) {
    if (!taskData) {
      throw new Error('任务数据不能为空');
    }
    
    if (!taskData.platform) {
      throw new Error('平台不能为空');
    }
    
    if (!taskData.content && !taskData.title) {
      throw new Error('内容和标题不能同时为空');
    }
    
    // 验证平台是否支持
    const supportedPlatforms = Array.from(this.plugin.platformAdapters?.keys() || []);
    if (!supportedPlatforms.includes(taskData.platform)) {
      throw new Error(`不支持的平台: ${taskData.platform}`);
    }
  }

  /**
   * 生成API令牌
   * @returns {string} API令牌
   */
  generateToken() {
    return `api_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * 关闭API服务
   */
  async close() {
    if (this.server) {
      await new Promise((resolve) => this.server.close(resolve));
      this.logger.log('API服务已关闭');
    }
  }
}

// 创建API服务实例并初始化
const apiService = new ApiService(AutoPublishPlugin);
apiService.init().catch(err => {
  console.error('API服务启动失败:', err);
});

// 导出API服务实例
module.exports = apiService; 