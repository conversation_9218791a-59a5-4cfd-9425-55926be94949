/**
 * 自动发布插件渲染进程入口文件
 * 负责渲染进程的初始化和通信
 */

// preload.js 内容整合到 renderer.js 中
// 使用 IIFE 将 preload 功能封装起来
(function() {
  const { contextBridge, ipcRenderer } = require('electron');

  // 检查是否已经存在electron对象
  if (typeof window !== 'undefined' && !window.electron) {
    // 向渲染进程暴露electron API接口
    contextBridge.exposeInMainWorld('electron', {
      // 调用主进程中注册的函数
      invoke: async (channel, ...args) => {
        // 检查允许的通道列表
        const validChannels = [
          'auto-publish:get-platforms', 
          'auto-publish:publish-content',
          'auto-publish:get-task',
          'auto-publish:get-task-status',
          'framework:get-plugin-api',
          'framework:call-plugin-api'
        ];
        
        if (!validChannels.includes(channel)) {
          console.warn(`尝试访问不安全的通道: ${channel}`);
          return { success: false, error: '不允许访问该通道' };
        }
        
        console.log(`调用主进程方法: ${channel}`, args);
        try {
          return await ipcRenderer.invoke(channel, ...args);
        } catch (error) {
          console.error(`调用 ${channel} 失败:`, error);
          // 返回一个包含错误信息的对象，而不是抛出异常
          return {
            success: false,
            error: `调用 ${channel} 失败: ${error.message}`
          };
        }
      },
      
      // 发送消息到主进程
      send: (channel, ...args) => {
        console.log(`发送消息到主进程: ${channel}`, args);
        ipcRenderer.send(channel, ...args);
      },
      
      // 监听主进程消息
      on: (channel, callback) => {
        console.log(`注册事件监听: ${channel}`);
        const wrappedCallback = (event, ...args) => callback(...args);
        ipcRenderer.on(channel, wrappedCallback);
        // 返回一个函数用于移除监听器
        return () => {
          ipcRenderer.removeListener(channel, wrappedCallback);
        };
      },
      
      // 移除事件监听
      removeListener: (channel, callback) => {
        ipcRenderer.removeListener(channel, callback);
      },
      
      // 专用于插件框架的通信方法
      plugin: {
        // 获取插件API
        getAPI: (pluginId) => {
          return ipcRenderer.invoke('framework:get-plugin-api', pluginId);
        },
        
        // 调用插件API方法
        callAPI: (pluginId, method, ...args) => {
          return ipcRenderer.invoke('framework:call-plugin-api', pluginId, method, ...args);
        }
      }
    });
  }

  console.log('自动发布插件预加载脚本已执行');
})();

// 当DOM加载完成后执行初始化
document.addEventListener('DOMContentLoaded', () => {
  // 初始化应用
  initApp();
});

/**
 * 初始化应用
 */
async function initApp() {
  console.log('自动发布插件渲染进程初始化');
  
  // 检查当前页面类型
  const currentPage = getCurrentPageType();
  
  // 根据页面类型加载对应模块
  switch (currentPage) {
    case 'main':
      // 加载主界面
      await loadMainView();
      break;
    case 'tasks':
      // 加载任务管理界面
      await loadTasksView();
      break;
    case 'settings':
      // 加载设置界面
      await loadSettingsView();
      break;
    default:
      // 默认加载主界面
      await loadMainView();
  }
  
  // 注册全局事件监听
  registerGlobalEvents();
}

/**
 * 获取当前页面类型
 * @returns {string} 页面类型
 */
function getCurrentPageType() {
  const pathname = window.location.pathname;
  
  if (pathname.includes('tasks.html')) {
    return 'tasks';
  } else if (pathname.includes('settings.html')) {
    return 'settings';
  } else {
    return 'main';
  }
}

/**
 * 加载主界面
 */
async function loadMainView() {
  try {
    // 获取平台列表
    const platforms = await window.electron.invoke('auto-publish:get-platforms');
    
    // 初始化平台选择器
    const platformSelector = document.getElementById('platform-selector');
    if (platformSelector) {
      platformSelector.innerHTML = '';
      
      platforms.forEach(platform => {
        const option = document.createElement('option');
        option.value = platform;
        option.textContent = getPlatformDisplayName(platform);
        platformSelector.appendChild(option);
      });
    }
    
    // 绑定表单提交事件
    const taskForm = document.getElementById('task-form');
    if (taskForm) {
      taskForm.addEventListener('submit', handleTaskSubmit);
    }
    
    // 初始化任务面板
    initTasksPanel();
    
  } catch (error) {
    showError('初始化主界面失败: ' + error.message);
  }
}

/**
 * 获取平台显示名称
 * @param {string} platform 平台标识
 * @returns {string} 平台显示名称
 */
function getPlatformDisplayName(platform) {
  const platformNames = {
    'kuaishou': '快手',
    'toutiao': '今日头条',
    'douyin': '抖音',
    'default': '默认平台'
  };
  
  return platformNames[platform] || platform;
}

/**
 * 处理任务提交
 * @param {Event} event 事件对象
 */
async function handleTaskSubmit(event) {
  event.preventDefault();
  
  try {
    const form = event.target;
    const platform = form.elements['platform'].value;
    const content = form.elements['content'].value;
    const account = form.elements['account'].value;
    
    if (!platform || !content || !account) {
      showError('请填写完整的任务信息');
      return;
    }
    
    // 显示加载状态
    showLoading('正在提交任务...');
    
    // 构建任务数据
    const taskData = {
      platform,
      content,
      account,
      createTime: new Date().toISOString()
    };
    
    // 提交任务
    const result = await window.electron.invoke('auto-publish:start-task', taskData);
    
    // 隐藏加载状态
    hideLoading();
    
    if (result.success) {
      showSuccess(`任务已提交，任务ID: ${result.taskId}`);
      form.reset();
      
      // 刷新任务列表
      updateTasksList();
    } else {
      showError('任务提交失败: ' + result.error);
    }
    
  } catch (error) {
    hideLoading();
    showError('提交任务失败: ' + error.message);
  }
}

/**
 * 初始化任务面板
 */
function initTasksPanel() {
  // 获取任务列表
  updateTasksList();
  
  // 设置定时刷新
  setInterval(updateTasksList, 10000); // 每10秒刷新一次
}

/**
 * 更新任务列表
 */
async function updateTasksList() {
  try {
    // 获取任务列表
    const tasks = await window.electron.invoke('auto-publish:get-tasks');
    
    // 更新DOM
    const tasksList = document.getElementById('tasks-list');
    if (tasksList) {
      tasksList.innerHTML = '';
      
      if (tasks.length === 0) {
        tasksList.innerHTML = '<div class="empty-message">暂无任务</div>';
        return;
      }
      
      tasks.forEach(task => {
        const taskItem = createTaskItem(task);
        tasksList.appendChild(taskItem);
      });
    }
  } catch (error) {
    console.error('更新任务列表失败:', error);
  }
}

/**
 * 创建任务列表项
 * @param {Object} task 任务数据
 * @returns {HTMLElement} 任务DOM元素
 */
function createTaskItem(task) {
  const item = document.createElement('div');
  item.className = `auto-publish-task-item status-${task.status.toLowerCase()}`;
  item.dataset.taskId = task.id;
  
  item.innerHTML = `
    <div class="auto-publish-task-header">
      <span class="auto-publish-task-platform">${getPlatformDisplayName(task.platform)}</span>
      <span class="auto-publish-task-status">${getStatusDisplayName(task.status)}</span>
    </div>
    <div class="auto-publish-task-content">${task.content.substring(0, 50)}${task.content.length > 50 ? '...' : ''}</div>
    <div class="auto-publish-task-footer">
      <span class="auto-publish-task-time">${formatTime(task.createTime)}</span>
      <div class="auto-publish-task-actions">
        ${task.status === 'PENDING' || task.status === 'RUNNING' ? 
          `<button class="auto-publish-btn auto-publish-btn-cancel" data-action="cancel" data-task-id="${task.id}">取消</button>` : ''}
        <button class="auto-publish-btn auto-publish-btn-detail" data-action="detail" data-task-id="${task.id}">详情</button>
      </div>
    </div>
  `;
  
  // 绑定按钮事件
  const cancelBtn = item.querySelector('[data-action="cancel"]');
  if (cancelBtn) {
    cancelBtn.addEventListener('click', () => handleCancelTask(task.id));
  }
  
  const detailBtn = item.querySelector('[data-action="detail"]');
  if (detailBtn) {
    detailBtn.addEventListener('click', () => showTaskDetail(task.id));
  }
  
  return item;
}

/**
 * 获取状态显示名称
 * @param {string} status 状态标识
 * @returns {string} 状态显示名称
 */
function getStatusDisplayName(status) {
  const statusNames = {
    'PENDING': '等待中',
    'RUNNING': '执行中',
    'SUCCESS': '已完成',
    'FAILED': '失败',
    'CANCELED': '已取消'
  };
  
  return statusNames[status] || status;
}

/**
 * 格式化时间
 * @param {string} timeString 时间字符串
 * @returns {string} 格式化后的时间
 */
function formatTime(timeString) {
  const date = new Date(timeString);
  return date.toLocaleString();
}

/**
 * 处理取消任务
 * @param {string} taskId 任务ID
 */
async function handleCancelTask(taskId) {
  try {
    if (!confirm('确定要取消此任务吗？')) {
      return;
    }
    
    // 显示加载状态
    showLoading('正在取消任务...');
    
    // 取消任务
    const result = await window.electron.invoke('auto-publish:cancel-task', taskId);
    
    // 隐藏加载状态
    hideLoading();
    
    if (result.success) {
      showSuccess('任务已取消');
      // 刷新任务列表
      updateTasksList();
    } else {
      showError('取消任务失败: ' + result.error);
    }
    
  } catch (error) {
    hideLoading();
    showError('取消任务失败: ' + error.message);
  }
}

/**
 * 显示任务详情
 * @param {string} taskId 任务ID
 */
async function showTaskDetail(taskId) {
  try {
    // 获取任务详情
    const taskDetail = await window.electron.invoke('auto-publish:get-task-detail', taskId);
    
    // 构建详情内容
    let detailHTML = `
      <h3>任务详情</h3>
      <div class="auto-publish-detail-item">
        <span class="auto-publish-detail-label">任务ID:</span>
        <span class="auto-publish-detail-value">${taskDetail.id}</span>
      </div>
      <div class="auto-publish-detail-item">
        <span class="auto-publish-detail-label">平台:</span>
        <span class="auto-publish-detail-value">${getPlatformDisplayName(taskDetail.platform)}</span>
      </div>
      <div class="auto-publish-detail-item">
        <span class="auto-publish-detail-label">状态:</span>
        <span class="auto-publish-detail-value status-${taskDetail.status.toLowerCase()}">${getStatusDisplayName(taskDetail.status)}</span>
      </div>
      <div class="auto-publish-detail-item">
        <span class="auto-publish-detail-label">创建时间:</span>
        <span class="auto-publish-detail-value">${formatTime(taskDetail.createTime)}</span>
      </div>
      <div class="auto-publish-detail-item">
        <span class="auto-publish-detail-label">内容:</span>
        <div class="auto-publish-detail-value content">${taskDetail.content}</div>
      </div>
    `;
    
    // 如果有日志，添加日志内容
    if (taskDetail.logs && taskDetail.logs.length > 0) {
      detailHTML += `
        <h4>执行日志</h4>
        <div class="auto-publish-task-logs">
          ${taskDetail.logs.map(log => `
            <div class="auto-publish-log-item">
              <span class="auto-publish-log-time">${formatTime(log.time)}</span>
              <span class="auto-publish-log-level ${log.level.toLowerCase()}">${log.level}</span>
              <span class="auto-publish-log-message">${log.message}</span>
            </div>
          `).join('')}
        </div>
      `;
    }
    
    // 如果有错误信息，添加错误详情
    if (taskDetail.error) {
      detailHTML += `
        <h4>错误信息</h4>
        <div class="auto-publish-task-error">
          ${taskDetail.error}
        </div>
      `;
    }
    
    // 显示详情对话框
    showDialog('任务详情', detailHTML);
    
  } catch (error) {
    showError('获取任务详情失败: ' + error.message);
  }
}

/**
 * 加载任务管理界面
 */
async function loadTasksView() {
  // 实现略，与loadMainView类似
}

/**
 * 加载设置界面
 */
async function loadSettingsView() {
  // 实现略，与loadMainView类似
}

/**
 * 注册全局事件监听
 */
function registerGlobalEvents() {
  // 监听任务状态更新
  window.electron.on('task:status-update', (data) => {
    updateTaskStatus(data.taskId, data.status);
  });
  
  // 监听主题变更
  window.electron.on('app:theme-changed', (theme) => {
    document.body.setAttribute('data-theme', theme);
  });
}

/**
 * 更新任务状态
 * @param {string} taskId 任务ID
 * @param {string} status 状态
 */
function updateTaskStatus(taskId, status) {
  const taskItem = document.querySelector(`.auto-publish-task-item[data-task-id="${taskId}"]`);
  if (taskItem) {
    // 更新状态类
    taskItem.className = taskItem.className.replace(/auto-publish-status-\w+/, `auto-publish-status-${status.toLowerCase()}`);
    
    // 更新状态文本
    const statusElement = taskItem.querySelector('.auto-publish-task-status');
    if (statusElement) {
      statusElement.textContent = getStatusDisplayName(status);
    }
    
    // 更新操作按钮
    const actionsContainer = taskItem.querySelector('.auto-publish-task-actions');
    if (actionsContainer) {
      // 根据状态决定是否显示取消按钮
      const cancelBtn = actionsContainer.querySelector('.auto-publish-btn-cancel');
      if (status === 'PENDING' || status === 'RUNNING') {
        if (!cancelBtn) {
          const newCancelBtn = document.createElement('button');
          newCancelBtn.className = 'auto-publish-btn auto-publish-btn-cancel';
          newCancelBtn.dataset.action = 'cancel';
          newCancelBtn.dataset.taskId = taskId;
          newCancelBtn.textContent = '取消';
          newCancelBtn.addEventListener('click', () => handleCancelTask(taskId));
          actionsContainer.insertBefore(newCancelBtn, actionsContainer.firstChild);
        }
      } else if (cancelBtn) {
        cancelBtn.remove();
      }
    }
  }
}

/**
 * 显示加载状态
 * @param {string} message 加载提示信息
 */
function showLoading(message = '加载中...') {
  // 检查是否已存在加载器
  if (document.querySelector('.auto-publish-loading-overlay')) {
    return;
  }
  
  const overlay = document.createElement('div');
  overlay.className = 'auto-publish-loading-overlay';
  overlay.innerHTML = `
    <div class="auto-publish-loading-container">
      <div class="auto-publish-loading-spinner"></div>
      <div class="auto-publish-loading-message">${message}</div>
    </div>
  `;
  
  document.body.appendChild(overlay);
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
  const overlay = document.querySelector('.auto-publish-loading-overlay');
  if (overlay) {
    overlay.remove();
  }
}

/**
 * 显示成功提示
 * @param {string} message 成功提示信息
 */
function showSuccess(message) {
  showNotification(message, 'success');
}

/**
 * 显示错误提示
 * @param {string} message 错误提示信息
 */
function showError(message) {
  showNotification(message, 'error');
}

/**
 * 显示通知
 * @param {string} message 通知信息
 * @param {string} type 通知类型：info, success, warning, error
 */
function showNotification(message, type = 'info') {
  // 创建通知容器（如果不存在）
  let notificationContainer = document.getElementById('auto-publish-notification-container');
  if (!notificationContainer) {
    notificationContainer = document.createElement('div');
    notificationContainer.id = 'auto-publish-notification-container';
    document.body.appendChild(notificationContainer);
  }
  
  // 创建通知
  const notification = document.createElement('div');
  notification.className = `auto-publish-notification auto-publish-${type}`;
  notification.innerHTML = `
    <div class="auto-publish-notification-icon">${getNotificationIcon(type)}</div>
    <div class="auto-publish-notification-content">${message}</div>
    <button class="auto-publish-notification-close">&times;</button>
  `;
  
  // 添加关闭事件
  const closeBtn = notification.querySelector('.auto-publish-notification-close');
  closeBtn.addEventListener('click', () => {
    notification.classList.add('auto-publish-notification-hiding');
    setTimeout(() => {
      notification.remove();
    }, 300);
  });
  
  // 添加到容器
  notificationContainer.appendChild(notification);
  
  // 自动消失
  setTimeout(() => {
    if (notification.parentNode) {
      notification.classList.add('auto-publish-notification-hiding');
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 300);
    }
  }, 5000);
}

/**
 * 获取通知图标
 * @param {string} type 通知类型
 * @returns {string} 图标HTML
 */
function getNotificationIcon(type) {
  switch (type) {
    case 'success':
      return '✓';
    case 'warning':
      return '⚠';
    case 'error':
      return '✗';
    default:
      return 'ℹ';
  }
}

/**
 * 显示对话框
 * @param {string} title 对话框标题
 * @param {string} content 对话框内容HTML
 */
function showDialog(title, content) {
  // 创建对话框
  const dialog = document.createElement('div');
  dialog.className = 'auto-publish-dialog-overlay';
  dialog.innerHTML = `
    <div class="auto-publish-dialog">
      <div class="auto-publish-dialog-header">
        <h3>${title}</h3>
        <button class="auto-publish-dialog-close">&times;</button>
      </div>
      <div class="auto-publish-dialog-content">
        ${content}
      </div>
    </div>
  `;
  
  // 添加关闭事件
  const closeBtn = dialog.querySelector('.auto-publish-dialog-close');
  closeBtn.addEventListener('click', () => {
    dialog.remove();
  });
  
  // 点击遮罩关闭
  dialog.addEventListener('click', (event) => {
    if (event.target === dialog) {
      dialog.remove();
    }
  });
  
  // 添加到页面
  document.body.appendChild(dialog);
}

// 导出公共方法供其他模块使用
window.autoPublish = {
  showLoading,
  hideLoading,
  showSuccess,
  showError,
  showDialog
}; 