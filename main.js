const { ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const BitBrowserManager = require('./static/js/core/BitBrowserManager');
const DynamicAdapterManager = require('./static/js/core/DynamicAdapterManager');
const FileUtils = require('./static/js/core/FileUtils');
const AutoPublishUtils = require('./static/js/core/AutoPublishUtils');
const BrowserManager = require('./static/js/core/BrowserManager');

/**
 * 自动发布插件主类
 * 实现插件的生命周期管理和核心功能
 */
class AutoPublishPlugin {
  /**
   * 构造函数
   */
  constructor() {
    this.context = null;
    this.pluginPath = '';
    this.logger = console;
    this.bitBrowserManager = null;
    this.dynamicAdapterManager = null;
    this.initialized = false;
    this.config = {
      dataPath: '',
      debug: true,
      apiPort: 3000,
      apiEnabled: true
    };
    
    // 初始化IPC监听
    this.setupIPC();
  }
  
  /**
   * 设置IPC通信
   */
  setupIPC() {
    // 确保ipcMain已被引入
    if (ipcMain) {
      // 发布内容
      ipcMain.handle('auto-publish:publish-content', async (event, taskData) => {
        return await this.publishContent(taskData);
      });
      
      // 获取平台列表
      ipcMain.handle('auto-publish:get-platforms', async (event) => {
        return this.getPlatforms();
      });
      
      this.logger.log('IPC通信通道已设置');
    } else {
      this.logger.error('无法设置IPC通信：ipcMain未定义');
    }
  }

  /**
   * 插件激活方法，由框架调用
   * @param {Object} context 插件上下文
   * @returns {Object} 插件API
   */
  async activate(context) {
    this.logger.log('自动发布插件开始激活');
    
    // 保存上下文
    this.context = context;
    this.pluginPath = context?.pluginPath || __dirname;
    
    // 初始化配置
    this.config = {
      ...this.config,
      ...(context.config || {}),
      dataPath: path.join(this.pluginPath, 'data')
    };
    
    // 创建数据目录
    if (!fs.existsSync(this.config.dataPath)) {
      fs.mkdirSync(this.config.dataPath, { recursive: true });
    }
    
    try {
      // 初始化核心模块
      await this.initializeCore();
      
      this.initialized = true;
      this.logger.log('自动发布插件激活成功');
      
      // 返回插件API
      return {
        publishContent: (taskData) => this.publishContent(taskData),
        getPlatforms: () => this.getPlatforms(),
        // API接口
        api: {
          handleRequest: (taskData, token) => this.apiReceiver.handleTaskRequest(taskData),
          getApiToken: () => this.apiReceiver.apiToken
        }
      };
    } catch (error) {
      this.logger.error('自动发布插件激活失败:', error);
      throw error;
    }
  }

  /**
   * 初始化核心模块
   */
  async initializeCore() {
    // 初始化BitBrowser管理器
    await this.initBitBrowserManager();
    
    // 初始化动态适配器管理器
    this.dynamicAdapterManager = new DynamicAdapterManager(this);
    
    // API服务会在http.js中自动初始化，不需要在这里初始化
  }

  /**
   * 初始化BitBrowser管理器
   */
  async initBitBrowserManager() {
    try {
      this.bitBrowserManager = new BitBrowserManager(this);
      await this.bitBrowserManager.init();
      this.logger.log('BitBrowser管理器初始化成功');
    } catch (error) {
      this.logger.error('初始化BitBrowser管理器失败', error);
      throw error;
    }
  }

  /**
   * 发布内容
   * @param {Object} taskData 任务数据
   * @returns {Promise<Object>} 发布结果
   */
  async publishContent(taskData) {
    if (!this.initialized) {
      return { success: false, error: '插件尚未初始化完成' };
    }
    
    try {
      const platform = taskData.platform || 'kuaishou';
      
      // 使用动态适配器管理器获取适配器
      const adapter = await this.dynamicAdapterManager.getAdapter(platform);
      
      if (!adapter) {
        throw new Error(`不支持的平台: ${platform}`);
      }
      
      const taskId = `task_${Date.now()}`;
      const context = { taskId, logger: this.logger };
      
      // 如果提供了browserId，在日志中记录
      if (taskData.browserId) {
        this.logger.log(`使用指定的浏览器ID进行发布: ${taskData.browserId}`);
      }
      
      // 执行发布
      const result = await adapter.publishContent(taskData, context);
      
      return {
        ...result,
        taskId
      };
    } catch (error) {
      this.logger.error('发布内容失败', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取支持的平台列表
   * @returns {Promise<string[]>} 平台列表
   */
  async getPlatforms() {
    try {
      // 从服务器获取支持的平台列表
      const response = await fetch('http://api.274500.xyz/api/platforms');
      if (!response.ok) {
        throw new Error(`获取平台列表失败: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      this.logger.error(`获取平台列表失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 插件停用方法，由框架调用
   */
  deactivate() {
    // 清理资源
    this.destroy().catch(error => {
      this.logger.error('清理资源失败', error);
    });
    
    // 清理引用
    this.dynamicAdapterManager = null;
    this.context = null;
    this.initialized = false;
    
    this.logger.log('自动发布插件已停用');
  }

  /**
   * 销毁插件
   */
  async destroy() {
    try {
      // 清理平台适配器资源
      if (this.dynamicAdapterManager) {
        await this.dynamicAdapterManager.cleanup();
      }
      
      this.logger.log('插件资源已清理');
    } catch (error) {
      this.logger.error('清理插件资源时出错', error);
    }
  }
}

// 导出插件类的实例，而不是类本身，确保只有一个实例
module.exports = new AutoPublishPlugin(); 