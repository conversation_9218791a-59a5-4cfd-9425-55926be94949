/**
 * BitBrowser管理器
 * 负责与BitBrowser插件交互，提供浏览器实例
 */
class BitBrowserManager {
  /**
   * 构造函数
   * @param {Object} plugin 插件实例
   */
  constructor(plugin) {
    this.plugin = plugin;
    this.logger = plugin.logger;
    this.bitBrowserAPI = null;
    this.initialized = false;
    this.currentBrowserId = null;
  }

  /**
   * 初始化BitBrowser管理器
   * @returns {Promise<void>}
   */
  async init() {
    try {
      // 获取BitBrowser插件API
      const bitBrowserPlugin = this.plugin.context?.getPlugin('bit-browser-plugin');
      
      if (!bitBrowserPlugin) {
        throw new Error('BitBrowser插件未找到');
      }
      
      this.bitBrowserAPI = bitBrowserPlugin.api;
      this.initialized = true;
      this.logger.log('BitBrowser管理器初始化成功');
    } catch (error) {
      this.logger.error('BitBrowser管理器初始化失败', error);
      throw error;
    }
  }

  /**
   * 获取浏览器实例
   * @returns {Promise<Object>} 浏览器实例
   */
  async getBrowser() {
    this.checkInitialized();
    
    try {
      if (!this.currentBrowserId) {
        // 获取默认浏览器ID
        const browsers = await this.bitBrowserAPI.getBrowsers();
        
        if (!browsers || browsers.length === 0) {
          throw new Error('没有可用的浏览器');
        }
        
        this.currentBrowserId = browsers[0].id;
      }
      
      // 启动浏览器
      this.logger.log(`正在启动浏览器: ${this.currentBrowserId}`);
      return await this.bitBrowserAPI.launchBrowser(this.currentBrowserId);
    } catch (error) {
      this.logger.error('获取浏览器实例失败', error);
      throw error;
    }
  }

  /**
   * 关闭浏览器
   * @returns {Promise<boolean>} 是否成功关闭
   */
  async closeBrowser() {
    this.checkInitialized();
    
    if (!this.currentBrowserId) {
      return true;
    }
    
    try {
      this.logger.log(`正在关闭浏览器: ${this.currentBrowserId}`);
      const result = await this.bitBrowserAPI.closeBrowser(this.currentBrowserId);
      this.currentBrowserId = null;
      return result;
    } catch (error) {
      this.logger.error(`关闭浏览器失败: ${this.currentBrowserId}`, error);
      return false;
    }
  }

  /**
   * 检查是否已初始化
   */
  checkInitialized() {
    if (!this.initialized) {
      throw new Error('BitBrowser管理器未初始化');
    }
  }
}

module.exports = BitBrowserManager; 