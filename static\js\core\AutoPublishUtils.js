/**
 * 自动发布工具类
 * 提供自动发布相关的通用功能
 */
const { chromium } = require('playwright');
const FileUtils = require('./FileUtils');

class AutoPublishUtils {
  /**
   * 解析内容数据
   * @param {Object} content 内容数据
   * @returns {Object} 解析后的内容数据
   */
  static parseContentData(content) {
    return {
      text: content.text || '',
      content: content.content || '',
      images: content.images || [],
      imagePath: content.imagePath || '',
      minImages: content.minImages || 1,
      maxImages: content.maxImages || 9,
      randomImages: content.randomImages || false
    };
  }

  /**
   * 连接到浏览器WebSocket
   * @param {string} wsEndpoint WebSocket地址
   * @param {Object} logger 日志对象
   * @returns {Promise<Object>} 浏览器实例和默认上下文
   */
  static async connectToBrowser(wsEndpoint, logger = console) {
    if (!wsEndpoint) {
      throw new Error('无效的WebSocket地址');
    }

    logger.log(`正在连接到浏览器WebSocket: ${wsEndpoint}`);
    
    try {
      // 连接到浏览器
      const browser = await chromium.connectOverCDP(wsEndpoint);
      
      // 获取默认上下文
      const context = browser.contexts()[0];
      if (!context) {
        throw new Error('无法获取浏览器上下文');
      }
      
      return { browser, context };
    } catch (error) {
      logger.error(`连接到浏览器失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 安全关闭浏览器连接
   * @param {Object} browser 浏览器实例
   * @param {Object} logger 日志对象 
   */
  static async closeBrowserConnection(browser, logger = console) {
    if (!browser) return;
    
    try {
      await browser.close();
      logger.log('已断开Playwright与浏览器的连接');
    } catch (error) {
      logger.error(`断开连接失败: ${error.message}`);
    }
  }

  /**
   * 等待元素存在并可见
   * @param {Object} page 页面对象
   * @param {string} selector 选择器
   * @param {Object} options 选项
   * @returns {Promise<boolean>} 是否存在并可见
   */
  static async waitForElementVisible(page, selector, options = {}) {
    const { timeout = 5000 } = options;
    try {
      await page.waitForSelector(selector, { 
        state: 'visible',
        timeout 
      });
      return true;
    } catch (error) {
      return false;
    }
  }
  
  /**
   * 检查页面是否包含指定文本
   * @param {Object} page 页面对象
   * @param {string} text 文本内容
   * @returns {Promise<boolean>} 是否包含文本
   */
  static async pageContainsText(page, text) {
    try {
      const content = await page.content();
      return content.includes(text);
    } catch (error) {
      return false;
    }
  }

  /**
   * 智能点击元素，尝试多种方式定位
   * @param {Object} page 页面对象
   * @param {string} text 文本内容
   * @param {Object} options 选项
   * @returns {Promise<boolean>} 是否点击成功
   */
  static async smartClick(page, text, options = {}) {
    const { exact = true, timeout = 5000, waitTime = 1000 } = options;
    
    try {
      // 方法1: 使用内置文本定位器
      await page.getByText(text, { exact }).click();
      await page.waitForTimeout(waitTime);
      return true;
    } catch (error) {
      try {
        // 方法2: 使用role+text组合定位
        await page.getByRole('button').getByText(text, { exact }).click();
        await page.waitForTimeout(waitTime);
        return true;
      } catch (error2) {
        try {
          // 方法3: 使用CSS选择器
          await page.locator(`button:has-text("${text}"), span:has-text("${text}")`).first().click();
          await page.waitForTimeout(waitTime);
          return true;
        } catch (error3) {
          return false;
        }
      }
    }
  }
  
  /**
   * 格式化错误响应
   * @param {Error} error 错误对象
   * @param {string} action 执行的操作
   * @returns {Object} 格式化的错误响应
   */
  static formatErrorResponse(error, action = '') {
    const actionText = action ? `${action}时` : '';
    return {
      success: false,
      error: `${actionText}出现错误: ${error.message}`
    };
  }

  /**
   * 生成随机发布时间
   * @param {Object} options 时间配置选项
   * @returns {string} 格式化的时间字符串
   */
  static generateRandomTime(options = {}) {
    const {
      startTime,
      endTime,
      timeMin,
      timeMax,
      scheduleTime = null,  // 初始时间
      platform = 'kuaishou'  // 平台类型
    } = options;

    // 解析开始和结束时间的小时
    const endsTime = parseInt(startTime.split(':')[0]);
    const startsTime = parseInt(endTime.split(':')[0]);
    
    // 如果有初始时间，使用初始时间作为基准
    const now = scheduleTime ? new Date(scheduleTime) : new Date();
    
    // 添加随机分钟数
    const minutesToAdd = Math.floor(Math.random() * (timeMax - timeMin + 1)) + parseInt(timeMin);
    now.setMinutes(now.getMinutes() + minutesToAdd);
    
    // 确保时间在指定范围内
    if (now.getHours() < startsTime) {
      now.setHours(startsTime, 0, 0);
    } else if (now.getHours() >= endsTime) {
      now.setDate(now.getDate() + 1);
      now.setHours(startsTime, 0, 0);
    }
    
    // 根据平台返回不同格式的时间
    switch (platform) {
      case 'douyin':
        // 抖音格式：YYYY-MM-DD HH:mm
        return now.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        }).replace(/\//g, '-');
        
      case 'kuaishou':
        // 快手格式：YYYY-MM-DD HH:mm:ss
        return now.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        }).replace(/\//g, '-');
        
      default:
        // 默认格式：YYYY-MM-DD HH:mm:ss
        return now.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        }).replace(/\//g, '-');
    }
  }

  /**
   * 获取图片路径列表
   * @param {Object} contentData 内容数据
   * @param {Object} logger 日志对象
   * @returns {Array} 有效的图片路径列表
   */
  static getImagePaths(contentData, logger = { log: console.log, error: console.error }) {
    const {
      imagePath,
      minImages = 3,
      maxImages = 3,
      randomImages = true
    } = contentData;

    // 如果提供了图片路径，优先使用
    if (contentData.images?.length > 0) {
      const validImages = contentData.images.filter(path => {
        const exists = FileUtils.fileExists(path);
        if (!exists) logger.error(`文件不存在: ${path}`);
        return exists;
      });
      if (validImages.length > 0) {
        return validImages;
      }
    }
    
    // 如果提供了图片目录，使用该目录
    if (imagePath) {
      const fileOptions = {
        minImages,
        maxImages,
        random: randomImages
      };
      const images = FileUtils.getImageFiles(imagePath, fileOptions, logger);
      if (images.length > 0) {
        return images;
      }
    }
    
    logger.error('没有找到有效的图片文件');
    return [];
  }

  /**
   * 处理内容模板和地区替换（严格按模板占位符替换，避免重复）
   * @param {Object} options 配置选项
   * @param {string} options.template 模板内容
   * @param {Object} options.regionNames 地区名称映射
   * @param {Array} options.regionIds 地区ID列表
   * @param {number} options.count 任务数量
   * @param {Object} logger 日志对象
   * @returns {Array} 处理后的内容列表
   */
  static processContentTemplate(options = {}, logger = { log: console.log }) {
    const {
      template = '',
      regionNames = {},
      regionIds = [],
      count = 1
    } = options;

    if (!template || !regionNames || !regionIds.length) {
      logger.error('缺少必要的模板或地区数据');
      return [];
    }

    // 直辖市/特殊市列表
    const directCities = ['北京市', '上海市', '天津市', '重庆市'];
    const results = [];
    let currentIndex = 0;

    for (let i = 0; i < count; i++) {
      // 按顺序获取地区ID
      const regionId = regionIds[currentIndex];
      const regionName = regionNames[regionId] || '';
      
      // 尝试获取上级市/省名（假设ID格式为 省_市_区 或 市_区）
      const idParts = regionId.split('_');
      let cityName = '';
      let provinceName = '';
      
      // 获取市名和省份名
      if (idParts.length >= 2) {
        cityName = regionNames[idParts[idParts.length - 2]] || '';
      }
      if (idParts.length >= 3) {
        provinceName = regionNames[idParts[0]] || '';
      }

      // 处理直辖市特殊情况
      let finalCity = cityName;
      let finalRegion = regionName;
      
      // 如果是直辖市
      if (directCities.includes(cityName) || directCities.includes(provinceName)) {
        // 如果区名包含"城区"，使用市名作为区名
        if (regionName.includes('城区')) {
          finalRegion = cityName;
          finalCity = '';
        }
        // 如果区名就是市名，使用市名作为区名
        else if (regionName === cityName) {
          finalRegion = cityName;
          finalCity = '';
        }
        // 如果是普通区县，保留市名
        else {
          finalCity = cityName;
        }
      }

      // 替换模板中的所有占位符
      let content = template;
      
      // 替换所有{市}和{区}的占位符
      content = content.replace(/{市}/g, finalCity).replace(/{区}/g, finalRegion);

      // 构建完整地区名称
      const fullRegionName = finalCity ? `${finalCity}${finalRegion}` : finalRegion;

      results.push({
        content,
        regionId,
        regionName: finalRegion,
        cityName: finalCity,
        provinceName,
        fullRegionName
      });

      logger.log(`生成第 ${i + 1} 条内容，市: ${finalCity} 区: ${finalRegion}`);
      currentIndex = (currentIndex + 1) % regionIds.length;
    }

    return results;
  }

  /**
   * 生成发布内容
   * @param {Object} options 配置选项
   * @param {string} options.template_title 标题模板
   * @param {string} options.template_content 内容模板
   * @param {Object} options.region_names 地区名称映射
   * @param {Array} options.region_ids 地区ID列表
   * @param {number} options.count 任务数量
   * @param {Object} logger 日志对象
   * @returns {Array} 生成的内容列表
   */
  static generatePublishContent(options = {}, logger = { log: console.log }) {
    const {
      template_title = '',
      template_content = '',
      region_names = {},
      region_ids = [],
      count = 1
    } = options;

    // 生成标题
    const titleResults = this.processContentTemplate({
      template: template_title,
      regionNames: region_names,
      regionIds: region_ids,
      count
    }, logger);

    // 生成内容
    const contentResults = this.processContentTemplate({
      template: template_content,
      regionNames: region_names,
      regionIds: region_ids,
      count
    }, logger);

    // 合并结果
    return titleResults.map((title, index) => ({
      title: title.content,
      text: contentResults[index]?.content || '',
      regionId: title.regionId,
      regionName: title.regionName,
      cityName: title.cityName,
      provinceName: title.provinceName
    }));
  }
}

module.exports = AutoPublishUtils; 