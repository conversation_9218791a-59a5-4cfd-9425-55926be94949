/**
 * 动态适配器管理器
 * 负责从服务器获取适配器代码并执行
 */
const BaseAdapter = require('../platforms/BaseAdapter');
const BrowserManager = require('../core/BrowserManager');
const path = require('path');
const fs = require('fs');

class DynamicAdapterManager {
  constructor(plugin) {
    this.plugin = plugin;
    this.logger = plugin.logger;
    this.adapters = new Map();
    this.adapterCache = new Map();
  }

  /**
   * 从服务器获取适配器代码
   * @param {string} platform 平台名称
   * @returns {Promise<string>} 适配器代码
   */
  async fetchAdapterCode(platform) {
    try {
      // 从服务器获取适配器代码
      const response = await fetch(`http://api.274500.xyz/api/adapter/${platform}`);
      if (!response.ok) {
        throw new Error(`获取适配器代码失败: ${response.statusText}`);
      }
      return await response.text();
    } catch (error) {
      this.logger.error(`获取适配器代码失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 编译适配器代码
   * @param {string} code 适配器代码
   * @param {string} platform 平台名称
   * @returns {Function} 编译后的适配器类
   */
  compileAdapterCode(code, platform) {
    try {
      // 创建上下文对象
      const context = {
        module: { exports: {} },
        console: this.logger
      };

      // 构建包装代码
      const wrapper = `
        ${code}
        module.exports = KuaishouAdapter;
      `;

      // 执行代码
      const fn = new Function(
        'BaseAdapter',
        'BrowserManager',
        'path',
        'require',
        'module',
        'console',
        wrapper
      );

      // 调用函数，传入所需依赖
      fn.call(
        context,
        BaseAdapter,
        BrowserManager,
        path,
        require,
        context.module,
        this.logger
      );

      // 返回编译后的适配器类
      return context.module.exports;
    } catch (error) {
      this.logger.error(`编译适配器代码失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取适配器实例
   * @param {string} platform 平台名称
   * @returns {Promise<BaseAdapter>} 适配器实例
   */
  async getAdapter(platform) {
    // 检查缓存
    if (this.adapters.has(platform)) {
      return this.adapters.get(platform);
    }

    try {
      // 获取适配器代码
      const code = await this.fetchAdapterCode(platform);
      
      // 编译适配器代码
      const AdapterClass = this.compileAdapterCode(code, platform);
      
      // 创建适配器实例
      const adapter = new AdapterClass(this.plugin);
      
      // 缓存适配器实例
      this.adapters.set(platform, adapter);
      
      return adapter;
    } catch (error) {
      this.logger.error(`获取适配器失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 清理适配器缓存
   * @param {string} [platform] 平台名称，如果不指定则清理所有缓存
   */
  clearCache(platform) {
    if (platform) {
      this.adapters.delete(platform);
      this.adapterCache.delete(platform);
    } else {
      this.adapters.clear();
      this.adapterCache.clear();
    }
  }
}

module.exports = DynamicAdapterManager; 