/**
 * 文件工具类
 * 提供文件处理相关的通用功能
 */
const fs = require('fs');
const path = require('path');

class FileUtils {
  /**
   * 获取指定目录下的图片文件列表
   * @param {string} directory 图片目录路径
   * @param {Object} options 选项
   * @param {number} options.minImages 最小图片数量
   * @param {number} options.maxImages 最大图片数量
   * @param {boolean} options.random 是否随机选择
   * @param {Object} logger 日志对象
   * @returns {Array<string>} 图片文件路径数组
   */
  static getImageFiles(directory, options = {}, logger = console) {
    const { minImages, maxImages, random } = options;
    
    try {
      // 确保目录存在
      if (!fs.existsSync(directory)) {
        logger.error(`图片目录不存在: ${directory}`);
        return [];
      }

      // 获取目录中的所有文件
      const files = fs.readdirSync(directory);
      
      // 筛选出jpg和png文件
      const imageFiles = files.filter(file => {
        const ext = path.extname(file).toLowerCase();
        return ext === '.jpg' || ext === '.jpeg' || ext === '.png';
      });
      
      if (imageFiles.length === 0) {
        logger.error(`目录中没有找到图片文件: ${directory}`);
        return [];
      }
      
      // 确定要选择的图片数量
      const numImages = Math.min(
        Math.max(Math.floor(Math.random() * (maxImages - minImages + 1)) + minImages, 1),
        imageFiles.length
      );
      
      let selectedImages;
      
      if (random) {
        // 随机选择指定数量的图片
        selectedImages = [];
        const shuffled = [...imageFiles].sort(() => 0.5 - Math.random());
        selectedImages = shuffled.slice(0, numImages);
      } else {
        // 按名称排序并选择
        const sortedImages = [...imageFiles].sort();
        selectedImages = sortedImages.slice(0, numImages);
      }
      
      // 返回完整路径
      return selectedImages.map(file => path.join(directory, file));
    } catch (error) {
      logger.error(`获取图片文件失败: ${error.message}`);
      return [];
    }
  }

  /**
   * 验证文件路径是否存在
   * @param {string} filePath 文件路径
   * @returns {boolean} 是否存在
   */
  static fileExists(filePath) {
    try {
      return fs.existsSync(filePath);
    } catch (error) {
      return false;
    }
  }

  /**
   * 确保目录存在，如不存在则创建
   * @param {string} dirPath 目录路径
   * @returns {boolean} 是否成功
   */
  static ensureDirectoryExists(dirPath) {
    try {
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取文件名（不含路径）
   * @param {string} filePath 文件路径
   * @returns {string} 文件名
   */
  static getFileName(filePath) {
    return path.basename(filePath);
  }

  /**
   * 获取文件扩展名
   * @param {string} filePath 文件路径
   * @returns {string} 扩展名（带.）
   */
  static getFileExtension(filePath) {
    return path.extname(filePath);
  }
}

module.exports = FileUtils; 