<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>自动发布插件</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      overflow: hidden;
    }
    
    #auto-publish-app {
      height: 100%;
      width: 100%;
      overflow: hidden;
    }
    
    .auto-publish-danger-btn {
      background-color: #dc3545 !important;
      border-color: #dc3545 !important;
    }
    
    .auto-publish-danger-btn:hover {
      background-color: #c82333 !important;
      border-color: #bd2130 !important;
    }
    
    /* 添加日志容器样式 */
    .auto-publish-logs-container {
      height: 300px;
      overflow-y: auto;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      background-color: #f8f9fa;
    }
    
    .auto-publish-logs-output {
      margin: 0;
      padding: 10px;
      white-space: pre-wrap;
      word-break: break-word;
    }
  </style>
</head>
<body>
  <div id="auto-publish-app"></div>
  <!-- 登录弹窗 -->
  <div id="auto-publish-login-modal" class="auto-publish-modal" style="display:none;position:fixed;left:0;top:0;width:100vw;height:100vh;z-index:9999;background:rgba(0,0,0,0.3);align-items:center;justify-content:center;">
    <div class="auto-publish-modal-content" style="background:#fff;padding:24px 32px;border-radius:8px;min-width:320px;box-shadow:0 2px 16px rgba(0,0,0,0.15);">
      <h3 style="margin-top:0;">登录以获取API令牌</h3>
      <div class="auto-publish-form-group">
        <label for="login-username">帐号</label>
        <input id="login-username" type="text" class="auto-publish-input" placeholder="请输入帐号">
      </div>
      <div class="auto-publish-form-group">
        <label for="login-password">密码</label>
        <input id="login-password" type="password" class="auto-publish-input" placeholder="请输入密码">
      </div>
      <div style="margin-top:18px;text-align:right;">
        <button id="login-cancel-btn" class="auto-publish-secondary-btn" style="margin-right:8px;">取消</button>
        <button id="login-confirm-btn" class="auto-publish-primary-btn">确认</button>
      </div>
    </div>
  </div>
  <script type="module">
    (async function() {
      try {
        const pluginId = 'auto-publish-plugin';
        const basePath = `plugin://plugins/${pluginId}/static/`;
        
        // 确保CSS加载
        const linkElement = document.createElement('link');
        linkElement.rel = 'stylesheet';
        linkElement.href = basePath + 'css/main.css';
        linkElement.setAttribute('data-loaded', 'true');
        document.head.appendChild(linkElement);
        
        // 加载API客户端脚本
        const apiClientScript = document.createElement('script');
        apiClientScript.src = basePath + 'js/api.js';
        document.body.appendChild(apiClientScript);
        
        // 等待API客户端脚本加载完成再加载主脚本
        await new Promise(resolve => {
          apiClientScript.onload = resolve;
        });
        
        const mainScript = document.createElement('script');
        mainScript.src = basePath + 'js/main.js';
        document.body.appendChild(mainScript);
        
        // 等待脚本加载完成
        await new Promise(resolve => {
          mainScript.onload = resolve;
        });
        
        // 初始化应用
        const container = document.getElementById('auto-publish-app');
        if (container && window.autoPublish) {
          // 构建UI结构
          container.innerHTML = `
            <div class="auto-publish-container">
              <!-- 选项卡导航 -->
              <div class="auto-publish-tabs">
                <button class="auto-publish-tab-btn active" data-tab="api">自动发布</button>
                <button class="auto-publish-tab-btn" data-tab="test">测试发布</button>
              </div>

              <!-- 选项卡内容 -->
              <div class="auto-publish-tab-content">
                <!-- API配置面板 -->
                <div class="auto-publish-tab-pane active" id="api-panel">
                  <div class="auto-publish-panel-heading">
                    <h2>API接口配置</h2>
                  </div>
                  <div class="auto-publish-api-info">
                    <div class="auto-publish-api-field">
                      <label>API令牌</label>
                      <div class="auto-publish-api-value-container">
                        <input id="api-token" type="text" class="auto-publish-input">
                        <button id="get-token-btn" class="auto-publish-secondary-btn">获取</button>
                      </div>
                    </div>
                    <div style="margin-top: 12px;">
                      <button id="start-receive-task-btn" class="auto-publish-primary-btn">获取任务</button>
                    </div>
                  </div>
                  <!-- 日志记录区域，原 logs-panel 内容 -->
                  <div class="auto-publish-panel-heading" style="margin-top:32px;">
                    <h2>操作日志</h2>
                    <div class="auto-publish-panel-actions">
                      <button id="clear-logs-btn" class="auto-publish-secondary-btn">清空日志</button>
                    </div>
                  </div>
                  <div class="auto-publish-logs-container" id="logs-container">
                    <pre id="logs-output" class="auto-publish-logs-output"></pre>
                  </div>
                </div>

                <!-- 测试发布面板 -->
                <div class="auto-publish-tab-pane" id="test-panel">
                  <div class="auto-publish-panel-heading">
                    <h2>测试内容发布</h2>
                  </div>
                  <form id="test-form" class="auto-publish-form">
                    <div class="auto-publish-form-group">
                      <label for="platform-input">平台ID</label>
                      <input type="text" id="platform-input" name="platform" class="auto-publish-input" placeholder="请输入平台ID" required>
                    </div>

                    <div class="auto-publish-form-group">
                      <label for="browser-id-input">浏览器ID</label>
                      <input type="text" id="browser-id-input" name="browserId" class="auto-publish-input" placeholder="请输入浏览器ID" required>
                    </div>

                    <div class="auto-publish-form-group">
                      <label for="content-title">标题</label>
                      <input type="text" id="content-title" name="title" class="auto-publish-input" placeholder="请输入内容标题">
                    </div>

                    <div class="auto-publish-form-group">
                      <label for="content-text">内容</label>
                      <textarea id="content-text" name="content" class="auto-publish-textarea" rows="6" placeholder="请输入发布内容" required></textarea>
                    </div>

                    <div class="auto-publish-form-group">
                      <label for="content-images">图片</label>
                      <div class="auto-publish-image-uploader">
                        <div id="image-preview" class="auto-publish-image-preview"></div>
                        <div class="auto-publish-image-actions">
                        <button type="button" id="add-image-btn" class="auto-publish-secondary-btn">添加图片</button>
                          <div class="auto-publish-image-status">
                            <span id="image-count">0</span> 张图片
                          </div>
                        </div>
                        <div id="image-preview-container" class="auto-publish-image-grid">
                          <!-- 图片预览将在这里显示 -->
                        </div>
                      </div>
                    </div>

                    <div class="auto-publish-form-group">
                      <label for="content-topics">话题标签</label>
                      <div class="auto-publish-topics-container">
                        <div id="topics-list" class="auto-publish-topics-list"></div>
                        <div class="auto-publish-topics-input-container">
                          <input type="text" id="topic-input" class="auto-publish-input" placeholder="输入话题">
                          <button type="button" id="add-topic-btn" class="auto-publish-secondary-btn">添加</button>
                        </div>
                      </div>
                    </div>

                    <div class="auto-publish-form-actions">
                      <button type="submit" class="auto-publish-primary-btn">发布内容</button>
                      <button type="reset" class="auto-publish-secondary-btn">重置</button>
                    </div>
                  </form>
                </div>
              </div>

              <!-- 通知容器 -->
              <div id="auto-publish-notification-container" class="auto-publish-notification-container"></div>
            </div>
          `;
          
          // 初始化应用后添加日志自动滚动处理
          // 添加日志滚动到底部的函数
          window.scrollLogsToBottom = function() {
            const logsContainer = document.getElementById('logs-container');
            if (logsContainer) {
              logsContainer.scrollTop = logsContainer.scrollHeight;
            }
          };
          
          // 监视日志内容变化
          if (window.MutationObserver) {
            const logsOutput = document.getElementById('logs-output');
            if (logsOutput) {
              const observer = new MutationObserver(function(mutations) {
                window.scrollLogsToBottom();
              });
              
              observer.observe(logsOutput, {
                childList: true,
                characterData: true,
                subtree: true
              });
            }
          }
          
          // 触发应用初始化
          if (typeof window.initApp === 'function') {
            window.initApp();
          }
        } else {
          throw new Error('找不到auto-publish-app容器元素或主脚本未正确加载');
        }
      } catch (error) {
        document.getElementById('auto-publish-app').innerHTML = `
          <div style="color: red; padding: 20px;">
            <h3>自动发布插件加载失败</h3>
            <p>${error.message || '未知错误'}</p>
            <p>详细信息：${error.stack || '无堆栈信息'}</p>
          </div>
        `;
        console.error('自动发布插件加载失败:', error);
      }
    })();
  </script>
</body>
</html> 