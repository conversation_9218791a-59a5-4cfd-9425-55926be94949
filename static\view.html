<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>自动发布插件</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      overflow: hidden;
    }

    #auto-publish-app {
      height: 100%;
      width: 100%;
      overflow: hidden;
    }

    .auto-publish-danger-btn {
      background-color: #dc3545 !important;
      border-color: #dc3545 !important;
    }

    .auto-publish-danger-btn:hover {
      background-color: #c82333 !important;
      border-color: #bd2130 !important;
    }

    /* 任务日志容器样式 */
    .auto-publish-task-logs-container {
      height: 400px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      background-color: #f8f9fa;
      display: flex;
      flex-direction: column;
    }

    /* 任务日志标签页 */
    .auto-publish-task-tabs {
      display: flex;
      border-bottom: 1px solid #e0e0e0;
      background-color: #fff;
      overflow-x: auto;
      flex-shrink: 0;
    }

    .auto-publish-task-tab {
      padding: 8px 16px;
      border: none;
      background: none;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      white-space: nowrap;
      font-size: 12px;
      color: #666;
      position: relative;
    }

    .auto-publish-task-tab.active {
      color: #007bff;
      border-bottom-color: #007bff;
      background-color: #f8f9fa;
    }

    .auto-publish-task-tab:hover {
      background-color: #f8f9fa;
    }

    .auto-publish-task-tab-close {
      margin-left: 8px;
      color: #999;
      font-weight: bold;
    }

    .auto-publish-task-tab-close:hover {
      color: #dc3545;
    }

    /* 任务日志内容区域 */
    .auto-publish-task-log-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .auto-publish-task-log-pane {
      display: none;
      flex: 1;
      flex-direction: column;
      overflow: hidden;
    }

    .auto-publish-task-log-pane.active {
      display: flex;
    }

    .auto-publish-task-log-output {
      flex: 1;
      overflow-y: auto;
      padding: 10px;
      margin: 0;
      white-space: pre-wrap;
      word-break: break-word;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
    }

    /* 任务日志操作按钮 */
    .auto-publish-task-log-actions {
      padding: 8px 10px;
      border-top: 1px solid #e0e0e0;
      background-color: #fff;
      display: flex;
      gap: 8px;
      flex-shrink: 0;
    }

    .auto-publish-task-log-actions button {
      padding: 4px 8px;
      font-size: 12px;
      border: 1px solid #ddd;
      background: #fff;
      border-radius: 3px;
      cursor: pointer;
    }

    .auto-publish-task-log-actions button:hover {
      background-color: #f8f9fa;
    }

    .auto-publish-task-log-actions .refresh-btn {
      color: #28a745;
      border-color: #28a745;
    }

    .auto-publish-task-log-actions .stop-btn {
      color: #dc3545;
      border-color: #dc3545;
    }

    .auto-publish-task-log-actions .history-btn {
      color: #17a2b8;
      border-color: #17a2b8;
    }

    .auto-publish-task-log-actions .close-btn {
      color: #6c757d;
      border-color: #6c757d;
    }

    /* 全局日志样式 */
    .auto-publish-logs-container {
      height: 300px;
      overflow-y: auto;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      background-color: #f8f9fa;
    }

    .auto-publish-logs-output {
      margin: 0;
      padding: 10px;
      white-space: pre-wrap;
      word-break: break-word;
    }

    /* 日志条目样式 */
    .log-entry {
      margin-bottom: 2px;
      padding: 2px 0;
    }

    .log-level-info { color: #17a2b8; }
    .log-level-success { color: #28a745; }
    .log-level-warning { color: #ffc107; }
    .log-level-error { color: #dc3545; }

    /* 空状态样式 */
    .auto-publish-empty-message {
      text-align: center;
      color: #999;
      padding: 20px;
      font-style: italic;
    }
  </style>
</head>
<body>
  <div id="auto-publish-app"></div>
  <!-- 登录弹窗 -->
  <div id="auto-publish-login-modal" class="auto-publish-modal" style="display:none;position:fixed;left:0;top:0;width:100vw;height:100vh;z-index:9999;background:rgba(0,0,0,0.3);align-items:center;justify-content:center;">
    <div class="auto-publish-modal-content" style="background:#fff;padding:24px 32px;border-radius:8px;min-width:320px;box-shadow:0 2px 16px rgba(0,0,0,0.15);">
      <h3 style="margin-top:0;">登录以获取API令牌</h3>
      <div class="auto-publish-form-group">
        <label for="login-username">帐号</label>
        <input id="login-username" type="text" class="auto-publish-input" placeholder="请输入帐号">
      </div>
      <div class="auto-publish-form-group">
        <label for="login-password">密码</label>
        <input id="login-password" type="password" class="auto-publish-input" placeholder="请输入密码">
      </div>
      <div style="margin-top:18px;text-align:right;">
        <button id="login-cancel-btn" class="auto-publish-secondary-btn" style="margin-right:8px;">取消</button>
        <button id="login-confirm-btn" class="auto-publish-primary-btn">确认</button>
      </div>
    </div>
  </div>
  <script type="module">
    (async function() {
      try {
        const pluginId = 'auto-publish-plugin';
        const basePath = `plugin://plugins/${pluginId}/static/`;

        // 确保CSS加载
        const linkElement = document.createElement('link');
        linkElement.rel = 'stylesheet';
        linkElement.href = basePath + 'css/main.css';
        linkElement.setAttribute('data-loaded', 'true');
        document.head.appendChild(linkElement);

        // 加载API客户端脚本
        const apiClientScript = document.createElement('script');
        apiClientScript.src = basePath + 'js/api.js';
        document.body.appendChild(apiClientScript);

        // 等待API客户端脚本加载完成再加载主脚本
        await new Promise(resolve => {
          apiClientScript.onload = resolve;
        });

        const mainScript = document.createElement('script');
        mainScript.src = basePath + 'js/main.js';
        document.body.appendChild(mainScript);

        // 等待脚本加载完成
        await new Promise(resolve => {
          mainScript.onload = resolve;
        });

        // 初始化应用
        const container = document.getElementById('auto-publish-app');
        if (container && window.autoPublish) {
          // 构建UI结构
          container.innerHTML = `
            <div class="auto-publish-container">
              <!-- 选项卡导航 -->
              <div class="auto-publish-tabs">
                <button class="auto-publish-tab-btn active" data-tab="api">自动发布</button>
                <button class="auto-publish-tab-btn" data-tab="test">测试发布</button>
              </div>

              <!-- 选项卡内容 -->
              <div class="auto-publish-tab-content">
                <!-- API配置面板 -->
                <div class="auto-publish-tab-pane active" id="api-panel">
                  <div class="auto-publish-panel-heading">
                    <h2>API接口配置</h2>
                  </div>
                  <div class="auto-publish-api-info">
                    <div class="auto-publish-api-field">
                      <label>API令牌</label>
                      <div class="auto-publish-api-value-container">
                        <input id="api-token" type="text" class="auto-publish-input">
                        <button id="get-token-btn" class="auto-publish-secondary-btn">获取</button>
                      </div>
                    </div>
                    <div style="margin-top: 12px;">
                      <button id="start-receive-task-btn" class="auto-publish-primary-btn">获取任务</button>
                    </div>
                  </div>
                  <!-- 任务日志管理区域 -->
                  <div class="auto-publish-panel-heading" style="margin-top:32px;">
                    <h2>任务日志</h2>
                    <div class="auto-publish-panel-actions">
                      <button id="clear-all-task-logs-btn" class="auto-publish-secondary-btn">清空所有日志</button>
                      <button id="show-global-logs-btn" class="auto-publish-secondary-btn">全局日志</button>
                    </div>
                  </div>
                  <div class="auto-publish-task-logs-container" id="task-logs-container">
                    <!-- 任务标签页 -->
                    <div class="auto-publish-task-tabs" id="task-tabs">
                      <button class="auto-publish-task-tab active" data-task-id="global">
                        全局日志
                        <span class="auto-publish-task-tab-close" onclick="event.stopPropagation(); closeTaskLog('global')">×</span>
                      </button>
                    </div>

                    <!-- 任务日志内容 -->
                    <div class="auto-publish-task-log-content" id="task-log-content">
                      <!-- 全局日志面板 -->
                      <div class="auto-publish-task-log-pane active" id="task-log-global">
                        <pre class="auto-publish-task-log-output" id="global-log-output"></pre>
                        <div class="auto-publish-task-log-actions">
                          <button class="refresh-btn" onclick="refreshGlobalLogs()">刷新</button>
                          <button class="close-btn" onclick="clearGlobalLogs()">清空</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 测试发布面板 -->
                <div class="auto-publish-tab-pane" id="test-panel">
                  <div class="auto-publish-panel-heading">
                    <h2>测试内容发布</h2>
                  </div>
                  <form id="test-form" class="auto-publish-form">
                    <div class="auto-publish-form-group">
                      <label for="platform-input">平台ID</label>
                      <input type="text" id="platform-input" name="platform" class="auto-publish-input" placeholder="请输入平台ID" required>
                    </div>

                    <div class="auto-publish-form-group">
                      <label for="browser-id-input">浏览器ID</label>
                      <input type="text" id="browser-id-input" name="browserId" class="auto-publish-input" placeholder="请输入浏览器ID" required>
                    </div>

                    <div class="auto-publish-form-group">
                      <label for="content-title">标题</label>
                      <input type="text" id="content-title" name="title" class="auto-publish-input" placeholder="请输入内容标题">
                    </div>

                    <div class="auto-publish-form-group">
                      <label for="content-text">内容</label>
                      <textarea id="content-text" name="content" class="auto-publish-textarea" rows="6" placeholder="请输入发布内容" required></textarea>
                    </div>

                    <div class="auto-publish-form-group">
                      <label for="content-images">图片</label>
                      <div class="auto-publish-image-uploader">
                        <div id="image-preview" class="auto-publish-image-preview"></div>
                        <div class="auto-publish-image-actions">
                        <button type="button" id="add-image-btn" class="auto-publish-secondary-btn">添加图片</button>
                          <div class="auto-publish-image-status">
                            <span id="image-count">0</span> 张图片
                          </div>
                        </div>
                        <div id="image-preview-container" class="auto-publish-image-grid">
                          <!-- 图片预览将在这里显示 -->
                        </div>
                      </div>
                    </div>

                    <div class="auto-publish-form-group">
                      <label for="content-topics">话题标签</label>
                      <div class="auto-publish-topics-container">
                        <div id="topics-list" class="auto-publish-topics-list"></div>
                        <div class="auto-publish-topics-input-container">
                          <input type="text" id="topic-input" class="auto-publish-input" placeholder="输入话题">
                          <button type="button" id="add-topic-btn" class="auto-publish-secondary-btn">添加</button>
                        </div>
                      </div>
                    </div>

                    <div class="auto-publish-form-actions">
                      <button type="submit" class="auto-publish-primary-btn">发布内容</button>
                      <button type="reset" class="auto-publish-secondary-btn">重置</button>
                    </div>
                  </form>
                </div>
              </div>

              <!-- 通知容器 -->
              <div id="auto-publish-notification-container" class="auto-publish-notification-container"></div>
            </div>
          `;

          // 初始化任务日志管理系统
          window.taskLogManager = {
            taskLogs: new Map(), // 存储每个任务的日志
            activeTaskId: 'global', // 当前激活的任务ID

            // 创建新的任务日志
            createTaskLog: function(taskId, taskName) {
              if (this.taskLogs.has(taskId)) {
                return; // 任务日志已存在
              }

              const taskLog = {
                id: taskId,
                name: taskName || `任务 ${taskId}`,
                logs: [],
                status: 'running', // running, completed, error, stopped
                startTime: new Date()
              };

              this.taskLogs.set(taskId, taskLog);
              this.createTaskTab(taskId, taskLog.name);
              this.switchToTask(taskId);
            },

            // 创建任务标签页
            createTaskTab: function(taskId, taskName) {
              const tabsContainer = document.getElementById('task-tabs');
              if (!tabsContainer) return;

              const tab = document.createElement('button');
              tab.className = 'auto-publish-task-tab';
              tab.setAttribute('data-task-id', taskId);
              tab.innerHTML = `
                ${taskName}
                <span class="auto-publish-task-tab-close" onclick="event.stopPropagation(); closeTaskLog('${taskId}')">×</span>
              `;
              tab.onclick = () => this.switchToTask(taskId);

              tabsContainer.appendChild(tab);

              // 创建对应的日志面板
              this.createTaskLogPane(taskId);
            },

            // 创建任务日志面板
            createTaskLogPane: function(taskId) {
              const contentContainer = document.getElementById('task-log-content');
              if (!contentContainer) return;

              const pane = document.createElement('div');
              pane.className = 'auto-publish-task-log-pane';
              pane.id = `task-log-${taskId}`;
              pane.innerHTML = `
                <pre class="auto-publish-task-log-output" id="${taskId}-log-output"></pre>
                <div class="auto-publish-task-log-actions">
                  <button class="refresh-btn" onclick="refreshTaskLog('${taskId}')">重新获取</button>
                  <button class="stop-btn" onclick="stopTask('${taskId}')">停止</button>
                  <button class="history-btn" onclick="showTaskHistory('${taskId}')">历史日志</button>
                  <button class="close-btn" onclick="closeTaskLog('${taskId}')">关闭</button>
                </div>
              `;

              contentContainer.appendChild(pane);
            },

            // 切换到指定任务
            switchToTask: function(taskId) {
              // 更新标签页状态
              document.querySelectorAll('.auto-publish-task-tab').forEach(tab => {
                tab.classList.toggle('active', tab.getAttribute('data-task-id') === taskId);
              });

              // 更新面板状态
              document.querySelectorAll('.auto-publish-task-log-pane').forEach(pane => {
                pane.classList.toggle('active', pane.id === `task-log-${taskId}`);
              });

              this.activeTaskId = taskId;
              this.scrollToBottom(taskId);
            },

            // 添加日志到指定任务
            addLog: function(taskId, level, message) {
              const taskLog = this.taskLogs.get(taskId);
              if (!taskLog) {
                // 如果任务不存在，添加到全局日志
                this.addGlobalLog(level, message);
                return;
              }

              const log = {
                time: new Date(),
                level,
                message
              };

              taskLog.logs.push(log);
              this.updateTaskLogDisplay(taskId);
            },

            // 添加全局日志
            addGlobalLog: function(level, message) {
              if (!window.autoPublish) {
                window.autoPublish = { logs: [] };
              }

              const log = {
                time: new Date(),
                level,
                message
              };

              window.autoPublish.logs.push(log);
              this.updateGlobalLogDisplay();
            },

            // 更新任务日志显示
            updateTaskLogDisplay: function(taskId) {
              const taskLog = this.taskLogs.get(taskId);
              if (!taskLog) return;

              const outputElement = document.getElementById(`${taskId}-log-output`);
              if (!outputElement) return;

              if (taskLog.logs.length === 0) {
                outputElement.innerHTML = '<div class="auto-publish-empty-message">暂无日志</div>';
                return;
              }

              outputElement.innerHTML = taskLog.logs.map(log => {
                const timeStr = log.time.toLocaleString();
                const levelClass = `log-level-${log.level.toLowerCase()}`;
                return `<div class="log-entry ${levelClass}">[${timeStr}] [${log.level}] ${log.message}</div>`;
              }).join('\n');

              this.scrollToBottom(taskId);
            },

            // 更新全局日志显示
            updateGlobalLogDisplay: function() {
              const outputElement = document.getElementById('global-log-output');
              if (!outputElement) return;

              if (!window.autoPublish || !window.autoPublish.logs || window.autoPublish.logs.length === 0) {
                outputElement.innerHTML = '<div class="auto-publish-empty-message">暂无日志</div>';
                return;
              }

              outputElement.innerHTML = window.autoPublish.logs.map(log => {
                const timeStr = log.time.toLocaleString();
                const levelClass = `log-level-${log.level.toLowerCase()}`;
                return `<div class="log-entry ${levelClass}">[${timeStr}] [${log.level}] ${log.message}</div>`;
              }).join('\n');

              this.scrollToBottom('global');
            },

            // 滚动到底部
            scrollToBottom: function(taskId) {
              const outputElement = document.getElementById(`${taskId}-log-output`);
              if (outputElement) {
                outputElement.scrollTop = outputElement.scrollHeight;
              }
            },

            // 关闭任务日志
            closeTask: function(taskId) {
              if (taskId === 'global') {
                // 不允许关闭全局日志，只清空
                this.clearGlobalLogs();
                return;
              }

              // 移除任务数据
              this.taskLogs.delete(taskId);

              // 移除标签页
              const tab = document.querySelector(`[data-task-id="${taskId}"]`);
              if (tab) {
                tab.remove();
              }

              // 移除面板
              const pane = document.getElementById(`task-log-${taskId}`);
              if (pane) {
                pane.remove();
              }

              // 如果关闭的是当前激活的任务，切换到全局日志
              if (this.activeTaskId === taskId) {
                this.switchToTask('global');
              }
            },

            // 清空全局日志
            clearGlobalLogs: function() {
              if (window.autoPublish) {
                window.autoPublish.logs = [];
              }
              this.updateGlobalLogDisplay();
            },

            // 清空所有任务日志
            clearAllLogs: function() {
              this.taskLogs.clear();
              this.clearGlobalLogs();

              // 移除所有任务标签页和面板（除了全局）
              document.querySelectorAll('.auto-publish-task-tab:not([data-task-id="global"])').forEach(tab => {
                tab.remove();
              });
              document.querySelectorAll('.auto-publish-task-log-pane:not(#task-log-global)').forEach(pane => {
                pane.remove();
              });

              this.switchToTask('global');
            }
          };

          // 全局函数定义
          window.closeTaskLog = function(taskId) {
            window.taskLogManager.closeTask(taskId);
          };

          window.refreshTaskLog = function(taskId) {
            // 重新获取任务逻辑
            window.taskLogManager.addLog(taskId, '信息', '重新获取任务...');
          };

          window.stopTask = function(taskId) {
            // 停止任务逻辑
            window.taskLogManager.addLog(taskId, '警告', '任务已停止');
            const taskLog = window.taskLogManager.taskLogs.get(taskId);
            if (taskLog) {
              taskLog.status = 'stopped';
            }
          };

          window.showTaskHistory = function(taskId) {
            // 显示历史日志逻辑
            window.taskLogManager.addLog(taskId, '信息', '显示历史日志...');
          };

          window.refreshGlobalLogs = function() {
            window.taskLogManager.updateGlobalLogDisplay();
          };

          window.clearGlobalLogs = function() {
            window.taskLogManager.clearGlobalLogs();
          };

          // 添加新按钮的事件监听器
          const clearAllTaskLogsBtn = document.getElementById('clear-all-task-logs-btn');
          if (clearAllTaskLogsBtn) {
            clearAllTaskLogsBtn.addEventListener('click', () => {
              window.taskLogManager.clearAllLogs();
            });
          }

          const showGlobalLogsBtn = document.getElementById('show-global-logs-btn');
          if (showGlobalLogsBtn) {
            showGlobalLogsBtn.addEventListener('click', () => {
              window.taskLogManager.switchToTask('global');
            });
          }

          // 初始化全局日志显示
          window.taskLogManager.updateGlobalLogDisplay();

          // 触发应用初始化
          if (typeof window.initApp === 'function') {
            window.initApp();
          }
        } else {
          throw new Error('找不到auto-publish-app容器元素或主脚本未正确加载');
        }
      } catch (error) {
        document.getElementById('auto-publish-app').innerHTML = `
          <div style="color: red; padding: 20px;">
            <h3>自动发布插件加载失败</h3>
            <p>${error.message || '未知错误'}</p>
            <p>详细信息：${error.stack || '无堆栈信息'}</p>
          </div>
        `;
        console.error('自动发布插件加载失败:', error);
      }
    })();
  </script>
</body>
</html>