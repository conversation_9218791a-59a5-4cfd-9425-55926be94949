/**
 * 快手平台适配器
 * 实现快手平台的内容发布功能
 */
const BrowserManager = require('../core/BrowserManager');
const BaseAdapter = require('./BaseAdapter');
const path = require('path');

class Ku<PERSON>houAdapter extends BaseAdapter {
  /**
   * 构造函数
   * @param {Object} plugin 插件实例
   */
  constructor(plugin) {
    super(plugin);
    this.platform = 'kuaishou';
    this.publishUrl = 'https://cp.kuaishou.com/article/publish/video?tabType=2';
    this.defaultUrl = 'https://cp.kuaishou.com/';
    this.urlPrefix = 'cp.kuaishou.com';
  }

  /**
   * 发布工作流
   * @param {string} accountId 账号ID
   * @returns {Promise<Object>} 发布结果
   */
  async publishWorkflow(accountId) {
    const session = this.sessions.get(accountId);
    if (!session) {
      throw new Error('浏览器会话无效');
    }

    // 获取页面
    const pageResult = await BrowserManager.findOrCreatePage(
      session, 
      this.urlPrefix, 
      this.defaultUrl, 
      this.logger
    );
    const page = pageResult.page;

    if (page.url().includes('passport.kuaishou.com')) {
      throw new Error('未登录，请先登录快手平台');
    }

    const config = this.configs.get(accountId);
    const count = config?.count || 1;
    const results = [];
    
    // 生成所有内容
    const publishContents = this.generatePublishContents(accountId);
    
    // 记录上一次的发布时间
    let lastPublishTime = null;
    
    for (let i = 0; i < count; i++) {
      this.sendLog('信息', `开始第 ${i + 1} 次发布`, accountId);
      
      try {
        await page.waitForLoadState('load');
        await page.goto(this.publishUrl);
        await this.wait(1000, accountId);
        
        // 获取图片路径
        const imagePaths = this.getImagePaths(accountId);
        
        if (imagePaths.length > 0) {
          const [fileChooser] = await Promise.all([
            page.waitForEvent('filechooser'),
            page.getByText('上传图片').click()
          ]);
            
          await fileChooser.setFiles(imagePaths.map(p => path.resolve(p)));
          this.sendLog('信息', '文件已设置到选择器', accountId);
          
          await this.wait(3000, accountId);
          
          const uploadStatus = await page.locator('.upload-status, .upload-progress').isVisible();
          this.sendLog('信息', '上传状态元素可见: ' + uploadStatus, accountId);
          
          if (uploadStatus) {
            await page.waitForSelector('.upload-status:not(:has-text("上传中"))', { timeout: 10000 });
            this.sendLog('信息', '图片上传完成', accountId);
          }
        }
        
        // 使用生成的内容
        const currentContent = publishContents[i];
        if (currentContent?.text) {
          await page.locator('#work-description-edit').fill(currentContent.text);
          this.sendLog('信息', `已填写内容，地区: ${currentContent.fullRegionName}`, accountId);
        }

        await page.locator('div').filter({ hasText: /^添加音乐$/ }).first().click();
        await this.wait(1000, accountId);
        
        const musicElements = await page.locator('.ant-drawer-body span:has(div:has-text("添加"))').all();
        if (musicElements.length > 0) {
          const randomElement = musicElements[Math.floor(Math.random() * musicElements.length)];
          await randomElement.scrollIntoViewIfNeeded();
          await this.wait(1000, accountId);
          
          const box = await randomElement.boundingBox();
          if (box) {
            await page.mouse.move(
              box.x + box.width / 2,
              box.y + box.height / 2
            );
            await this.wait(1000, accountId);
          }
          
          await randomElement.click();
          this.sendLog('信息', '已添加音乐', accountId);
        } else {
          this.sendLog('警告', '未找到可用的音乐', accountId);
        }
        await this.wait(1000, accountId);

        // 生成发布时间，使用上一次的时间作为基准
        const publishTime = this.generatePublishTime(lastPublishTime, accountId);
        lastPublishTime = publishTime;  // 更新上一次的发布时间
        
        await page.getByText('定时发布', { exact: true }).click();
        await this.wait(1000, accountId);
        
        const timeInput = page.getByPlaceholder('选择日期时间');
        await timeInput.click();
        await timeInput.fill('');
        await timeInput.type(publishTime);
        await this.wait(1000, accountId);
        
        await page.getByRole('button', { name: '确定' }).click();
        await this.wait(1000, accountId);
        
        await page.getByText('发布', { exact: true }).click();
        await this.wait(2000, accountId);
        
        results.push({ 
          success: true,
          message: `第 ${i + 1} 次发布成功，计划发布时间: ${publishTime}`,
          index: i + 1,
          publishTime,
          content: currentContent
        });
        
        this.sendLog('成功', `第 ${i + 1} 次发布成功，计划发布时间: ${publishTime}`, accountId);
        await this.wait(2000, accountId);
      
      } catch (error) {
        this.sendLog('错误', `第 ${i + 1} 次发布失败: ${error.message}`, accountId);
        throw error;
      }
    }
    
    const successCount = results.filter(r => r.success).length;
    return {
      success: successCount === results.length,
      message: `发布完成，成功: ${successCount}，失败: ${results.length - successCount}`,
      details: results
    };
  }
}

module.exports = KuaishouAdapter; 