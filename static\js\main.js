/**
 * 自动发布插件 - 前端主脚本
 */
(function() {
  // 检测环境并确保electron对象可用
  function ensureElectronAvailable() {
    // 只保留Electron/框架通信部分
    if (window.electron && typeof window.electron.invoke === 'function') {
      console.log('检测到Electron环境，使用原生通信');
      return;
    }
    // 检查是否在插件框架环境中
    const framework = window.pluginFramework ||
                    (window.parent && window.parent.pluginFramework);
    if (framework) {
      console.log('检测到插件框架环境，使用框架通信');
      setupFrameworkAdapter(framework);
      return;
    }
    // 不再提供模拟适配器，直接提示环境不支持
    alert('未检测到支持的运行环境，请在Electron或插件框架下运行本插件。');
  }

  // 设置框架适配器
  function setupFrameworkAdapter(framework) {
    const pluginId = 'auto-publish-plugin';

    window.electron = window.electron || {};
    const originalInvoke = window.electron.invoke;

    window.electron.invoke = async (channel, ...args) => {
      try {
        // 尝试使用框架API
        if (framework.invokePluginMethod) {
          return await framework.invokePluginMethod(pluginId, channel, ...args);
        }

        // 尝试使用原始方法
        if (originalInvoke) {
          return await originalInvoke(channel, ...args);
        }

        // 如果都失败，使用模拟处理
        return await mockHandleRequest(channel, ...args);
    } catch (error) {
        console.error(`调用方法失败: ${channel}`, error);
        return {
          success: false,
          error: error.message || '未知错误'
        };
      }
    };

    window.electron.send = window.electron.send || function(channel, ...args) {
      console.log(`框架模式发送: ${channel}`, args);
    };

    window.electron.on = window.electron.on || function(channel, callback) {
      console.log(`框架模式监听: ${channel}`);
      return () => {};
    };
  }

  // 确保electron对象可用
  ensureElectronAvailable();

  // 全局变量
  window.autoPublish = {
    platforms: [],
    imageFiles: [],
    topics: [],
    logs: [],
    browserInfo: null
  };

  // 初始化应用
  window.initApp = async function() {
    setupEventListeners();
    await loadAPIInfo();
    addLog('信息', '插件初始化完成');
  };

  // 设置事件监听器
  function setupEventListeners() {
    // 选项卡切换
    document.querySelectorAll('.auto-publish-tab-btn').forEach(button => {
      button.addEventListener('click', function() {
        const tabName = this.getAttribute('data-tab');
        switchTab(tabName);
      });
    });

    // 表单提交处理
    const testForm = document.getElementById('test-form');
    if (testForm) {
      testForm.addEventListener('submit', handleFormSubmit);
    }

    // 话题添加
    const addTopicBtn = document.getElementById('add-topic-btn');
    if (addTopicBtn) {
      addTopicBtn.addEventListener('click', addTopic);
    }

    // 话题输入按回车
    const topicInput = document.getElementById('topic-input');
    if (topicInput) {
      topicInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
          e.preventDefault();
          addTopic();
        }
      });
    }

    // 图片添加
    const addImageBtn = document.getElementById('add-image-btn');
    if (addImageBtn) {
      addImageBtn.addEventListener('click', openImagePicker);
    }

    // API令牌获取
    const getTokenBtn = document.getElementById('get-token-btn');
    if (getTokenBtn) {
      getTokenBtn.addEventListener('click', () => {
        showLoginModal();
      });
    }

    // 开始接收任务按钮
    const startReceiveTaskBtn = document.getElementById('start-receive-task-btn');
    if (startReceiveTaskBtn) {
      startReceiveTaskBtn.addEventListener('click', () => {
        startTaskPolling();
      });
    }

    // 清空日志按钮
    const clearLogsBtn = document.getElementById('clear-logs-btn');
    if (clearLogsBtn) {
      clearLogsBtn.addEventListener('click', clearLogs);
    }
  }

  // 复制到剪贴板
  function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    if (!element) return;

    element.select();
    document.execCommand('copy');

    showNotification('成功', '已复制到剪贴板', 'success');
  }

  // 切换选项卡
  function switchTab(tabName) {
    // 更新按钮状态
    document.querySelectorAll('.auto-publish-tab-btn').forEach(button => {
      button.classList.toggle('active', button.getAttribute('data-tab') === tabName);
    });

    // 更新面板状态
    document.querySelectorAll('.auto-publish-tab-pane').forEach(panel => {
      panel.classList.remove('active');
    });

    const targetPanel = document.getElementById(`${tabName}-panel`);
    if (targetPanel) {
      targetPanel.classList.add('active');
    }

    // 加载面板特定内容
    switch (tabName) {
      case 'api':
        loadAPIInfo();
        updateLogsDisplay();
        break;
      case 'browser':
        loadBrowserInfo();
        break;
    }
  }

  // 加载API信息
  async function loadAPIInfo() {
    try {
      // 清空token输入框，等待用户点击获取按钮
      const tokenInput = document.getElementById('api-token');
      if (tokenInput) {
        tokenInput.value = '';
      }

      addLog('信息', '请点击"获取"按钮登录获取令牌');
    } catch (error) {
      console.error('加载API信息失败:', error);
      showNotification('错误', '加载API信息失败: ' + error.message, 'error');
      addLog('错误', '加载API信息失败: ' + error.message);
    }
  }

  // 添加日志 - 支持任务日志系统
  function addLog(level, message, taskId = null) {
    // 如果指定了任务ID且任务日志管理器可用，添加到任务日志
    if (taskId && window.taskLogManager) {
      window.taskLogManager.addLog(taskId, level, message);
      return;
    }

    // 否则添加到全局日志
    if (window.taskLogManager) {
      window.taskLogManager.addGlobalLog(level, message);
    } else {
      // 兼容旧系统
      const log = {
        time: new Date(),
        level,
        message
      };

      if (!window.autoPublish) {
        window.autoPublish = { logs: [] };
      }
      window.autoPublish.logs.push(log);

      // 只要api-panel可见就更新日志
      const apiPanel = document.getElementById('api-panel');
      if (apiPanel && apiPanel.classList.contains('active')) {
        updateLogsDisplay();
      }
    }
  }

  // 清空日志
  function clearLogs() {
    if (window.taskLogManager) {
      window.taskLogManager.clearGlobalLogs();
    } else {
      window.autoPublish.logs = [];
      updateLogsDisplay();
    }
    showNotification('成功', '日志已清空', 'success');
  }

  // 更新日志显示 - 兼容旧系统
  function updateLogsDisplay() {
    if (window.taskLogManager) {
      window.taskLogManager.updateGlobalLogDisplay();
      return;
    }

    // 兼容旧系统
    const logsOutput = document.getElementById('logs-output');
    if (!logsOutput) return;

    if (!window.autoPublish || !window.autoPublish.logs || window.autoPublish.logs.length === 0) {
      logsOutput.innerHTML = '<div class="auto-publish-empty-message">暂无日志</div>';
      return;
    }

    logsOutput.innerHTML = window.autoPublish.logs.map(log => {
      const timeStr = log.time.toLocaleString();
      const levelClass = `log-level-${log.level.toLowerCase()}`;

      return `<div class="log-entry ${levelClass}">[${timeStr}] [${log.level}] ${log.message}</div>`;
    }).join('\n');

    // 滚动到底部
    logsOutput.scrollTop = logsOutput.scrollHeight;
  }

  // 处理表单提交
  async function handleFormSubmit(event) {
    event.preventDefault();
    try {
      const form = event.target;
      const formData = new FormData(form);
      const browserId = formData.get('browserId');
      if (!browserId) {
        throw new Error('请输入浏览器ID');
      }

      // 检查是否有API令牌 - 只用于任务获取，不用于发布
      const tokenInput = document.getElementById('api-token');
      if (!tokenInput || !tokenInput.value.trim()) {
        showNotification('错误', '请先获取API令牌', 'error');
        addLog('错误', '发布内容失败: 未获取API令牌');
        return;
      }

      if (window.autoPublish.imageFiles.length === 0) {
        addLog('警告', '未选择任何图片，将进行纯文本发布');
      } else {
        addLog('信息', `准备发布${window.autoPublish.imageFiles.length}张图片`);
      }
      const taskData = {
        platform: formData.get('platform'),
        title: formData.get('title'),
        content: formData.get('content'),
        browserId: browserId,
        images: window.autoPublish.imageFiles.length > 0 ? window.autoPublish.imageFiles : undefined,
        topics: window.autoPublish.topics.length > 0 ? window.autoPublish.topics : undefined
      };
      if (!taskData.platform) {
        throw new Error('请输入平台ID');
      }
      if (!taskData.content && !taskData.title) {
        throw new Error('标题和内容不能同时为空');
      }
      if (taskData.images && taskData.images.length > 9) {
        throw new Error('最多只能发布9张图片');
      }
      addLog('信息', `发布平台: ${taskData.platform}`);
      addLog('信息', `浏览器ID: ${browserId}`);
      if (taskData.title) addLog('信息', `标题: ${taskData.title.substring(0, 30)}${taskData.title.length > 30 ? '...' : ''}`);
      if (taskData.content) addLog('信息', `内容长度: ${taskData.content.length}字符`);
      if (taskData.images) addLog('信息', `图片数量: ${taskData.images.length}`);
      if (taskData.topics) addLog('信息', `话题: ${taskData.topics.join(', ')}`);

      // 使用Electron IPC调用插件发布内容，而不是通过API接口
      addLog('信息', '调用主进程发布内容...');

      // 设置事件监听器来接收实时日志
      const logHandler = (event, logData) => {
        addLog(logData.level, logData.message);
      };

      // 注册事件监听器
      window.electron.on('auto-publish:log', logHandler);

      try {
        const result = await window.electron.invoke('auto-publish:publish-content', taskData);

        if (result.success) {
          showNotification('成功', '内容已成功提交发布', 'success');
          addLog('成功', '内容发布成功: ' + (result.message || ''));
          resetForm(form);
        } else {
          throw new Error(result.error || '发布失败');
        }
      } finally {
        // 清理事件监听器
        if (window.electron.off) {
          window.electron.off('auto-publish:log', logHandler);
        } else if (window.electron.removeEventListener) {
          window.electron.removeEventListener('auto-publish:log', logHandler);
        } else {
          // 如果都不支持，至少记录一个警告
          console.warn('无法清理事件监听器：未找到合适的清理方法');
        }
      }
    } catch (error) {
      console.error('发布失败:', error);
      showNotification('错误', '发布失败: ' + error.message, 'error');
      addLog('错误', '发布失败: ' + error.message);
    }
  }

  // 重置表单
  function resetForm(form) {
    if (!form) return;

    form.reset();

    // 清空话题
    window.autoPublish.topics = [];
    updateTopicsList();

    // 清空图片
    window.autoPublish.imageFiles = [];
    updateImagePreview();

    addLog('信息', '表单已重置');
  }

  // 添加话题
  function addTopic() {
    const topicInput = document.getElementById('topic-input');
    if (!topicInput) return;

    const topic = topicInput.value.trim();
    if (!topic) return;

    // 检查话题是否已存在
    if (window.autoPublish.topics.includes(topic)) {
      showNotification('提示', '该话题已添加', 'info');
        return;
      }

    // 添加话题
    window.autoPublish.topics.push(topic);
    addLog('信息', `添加话题: ${topic}`);

    // 更新UI
    updateTopicsList();

    // 清空输入框
    topicInput.value = '';
    topicInput.focus();
  }

  // 更新话题列表
  function updateTopicsList() {
    const topicsList = document.getElementById('topics-list');
    if (!topicsList) return;

    const topics = window.autoPublish.topics;

    if (topics.length > 0) {
      topicsList.innerHTML = topics.map((topic, index) => `
        <div class="auto-publish-topic-chip">
          <span>${topic}</span>
          <button type="button" class="auto-publish-topic-remove" data-index="${index}">×</button>
        </div>
      `).join('');

      // 绑定删除按钮事件
      topicsList.querySelectorAll('.auto-publish-topic-remove').forEach(button => {
        button.addEventListener('click', function() {
          const index = parseInt(this.getAttribute('data-index'), 10);
          if (!isNaN(index)) {
            const removedTopic = window.autoPublish.topics[index];
            window.autoPublish.topics.splice(index, 1);
            addLog('信息', `移除话题: ${removedTopic}`);
            updateTopicsList();
          }
        });
      });
      } else {
      topicsList.innerHTML = '';
    }
  }

  // 打开图片选择器
  function openImagePicker() {
    // 模拟文件选择
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.multiple = true;

    input.onchange = function() {
      if (input.files && input.files.length > 0) {
        const fileCount = input.files.length;
        addLog('信息', `已选择${fileCount}个图片文件`);

        // 检查图片数量限制
        if (window.autoPublish.imageFiles.length + fileCount > 9) {
          showNotification('警告', '最多只能上传9张图片', 'warning');
          return;
        }

        // 将选择的文件添加到图片数组
        for (let i = 0; i < input.files.length; i++) {
          const file = input.files[i];

          // 检查文件类型
          if (!file.type.match('image/(jpeg|jpg|png|gif)')) {
            showNotification('警告', `不支持的图片格式: ${file.name}`, 'warning');
            continue;
          }

          // 检查文件大小 (限制为10MB)
          if (file.size > 10 * 1024 * 1024) {
            showNotification('警告', `图片过大: ${file.name}`, 'warning');
            continue;
          }

          window.autoPublish.imageFiles.push(file.path);
        }

        // 更新预览
        updateImagePreview();
        showNotification('成功', `已添加${window.autoPublish.imageFiles.length}张图片`, 'success');
      }
    };

    input.click();
  }

  // 更新图片预览
  function updateImagePreview() {
    const imagePreview = document.getElementById('image-preview');
    const imagePreviewContainer = document.getElementById('image-preview-container');
    const imageCount = document.getElementById('image-count');

    if (!imagePreview || !imagePreviewContainer) return;

    const images = window.autoPublish.imageFiles;

    // 更新图片数量显示
    if (imageCount) {
      imageCount.textContent = images.length;
    }

    if (images.length > 0) {
      // 清空之前的预览
      imagePreview.innerHTML = '';

      // 在图片预览容器中显示缩略图
      imagePreviewContainer.innerHTML = images.map((imagePath, index) => {
        // 构建图片预览项
        return `
        <div class="auto-publish-image-item">
            <div class="auto-publish-image-preview-wrapper">
              <img src="file://${imagePath}" alt="${getFileName(imagePath)}" class="auto-publish-image-thumbnail" />
              <div class="auto-publish-image-overlay">
                <span class="auto-publish-image-name">${getFileName(imagePath)}</span>
                <button type="button" class="auto-publish-image-remove" data-index="${index}" title="删除图片">×</button>
              </div>
            </div>
      </div>
        `;
      }).join('');

      // 绑定删除按钮事件
      imagePreviewContainer.querySelectorAll('.auto-publish-image-remove').forEach(button => {
        button.addEventListener('click', function() {
          const index = parseInt(this.getAttribute('data-index'), 10);
          if (!isNaN(index)) {
            const removedImage = window.autoPublish.imageFiles[index];
            window.autoPublish.imageFiles.splice(index, 1);
            addLog('信息', `移除图片: ${getFileName(removedImage)}`);
            updateImagePreview();
          }
        });
      });

      // 显示图片预览容器
      imagePreviewContainer.style.display = 'grid';
    } else {
      // 没有图片时清空预览
      imagePreview.innerHTML = '';
      imagePreviewContainer.innerHTML = '<div class="auto-publish-no-images">未添加图片</div>';
    }
  }

  // 获取文件名
  function getFileName(path) {
    if (!path) return '';
    return path.split('\\').pop().split('/').pop();
  }

  // 显示通知
  function showNotification(title, message, type = 'info', duration = 3000) {
    const container = document.getElementById('auto-publish-notification-container');
    if (!container) return;

    const notification = document.createElement('div');
    notification.className = `auto-publish-notification auto-publish-notification-${type}`;
    notification.innerHTML = `
      <div class="auto-publish-notification-title">${title}</div>
      <div class="auto-publish-notification-message">${message}</div>
      <button class="auto-publish-notification-close">×</button>
    `;

    container.appendChild(notification);

    // 添加关闭按钮事件
    const closeBtn = notification.querySelector('.auto-publish-notification-close');
    closeBtn.addEventListener('click', () => {
      notification.classList.add('auto-publish-notification-hiding');
      setTimeout(() => {
        if (container.contains(notification)) {
          container.removeChild(notification);
        }
      }, 300);
    });

    // 显示动画
    setTimeout(() => {
      notification.classList.add('auto-publish-notification-visible');
    }, 10);

    // 自动关闭
    setTimeout(() => {
        notification.classList.add('auto-publish-notification-hiding');
        setTimeout(() => {
        if (container.contains(notification)) {
          container.removeChild(notification);
          }
        }, 300);
    }, duration);
  }

  // 登录弹窗相关
  const loginModal = document.getElementById('auto-publish-login-modal');
  const loginUsername = document.getElementById('login-username');
  const loginPassword = document.getElementById('login-password');
  const loginCancelBtn = document.getElementById('login-cancel-btn');
  const loginConfirmBtn = document.getElementById('login-confirm-btn');

  function showLoginModal() {
    if (loginModal) {
      loginModal.style.display = 'flex';
      if (loginUsername) loginUsername.value = '';
      if (loginPassword) loginPassword.value = '';
      if (loginUsername) loginUsername.focus();
    }
  }
  function hideLoginModal() {
    if (loginModal) loginModal.style.display = 'none';
  }
  if (loginCancelBtn) {
    loginCancelBtn.addEventListener('click', hideLoginModal);
  }
  if (loginConfirmBtn) {
    loginConfirmBtn.addEventListener('click', async () => {
      const username = loginUsername ? loginUsername.value.trim() : '';
      const password = loginPassword ? loginPassword.value : '';
      if (!username || !password) {
        showNotification('错误', '请输入帐号和密码', 'error');
        return;
      }
      try {
        // 使用API客户端获取API令牌
        const apiInfo = await window.apiClient.getApiToken(username, password);
        if (apiInfo.success) {
          const tokenInput = document.getElementById('api-token');
          if (tokenInput) {
            tokenInput.value = apiInfo.token || '';
          }
          showNotification('成功', 'API令牌获取成功', 'success');
          hideLoginModal();
          // 不再加载平台列表
        } else {
          throw new Error(apiInfo.error || 'API令牌获取失败');
        }
      } catch (error) {
        showNotification('错误', 'API令牌获取失败: ' + error.message, 'error');
      }
    });
  }

  // 开始轮询任务
  function startTaskPolling() {
    // 检查是否有API令牌
    const tokenInput = document.getElementById('api-token');
    if (!tokenInput || !tokenInput.value.trim()) {
      showNotification('错误', '请先获取API令牌', 'error');
      addLog('错误', '获取任务失败: 未获取API令牌');
      return;
    }

    const token = tokenInput.value.trim();

    // 更新按钮状态 - 只在请求期间禁用按钮
    const startReceiveTaskBtn = document.getElementById('start-receive-task-btn');
    if (startReceiveTaskBtn) {
      startReceiveTaskBtn.disabled = true;
      startReceiveTaskBtn.textContent = '正在获取任务...';
    }

    addLog('信息', '开始获取任务列表...');

    // 一次性获取所有任务
    (async () => {
      try {
        // 获取所有待处理任务
        const result = await window.apiClient.getAllTasks(token);

        if (result.success) {
          if (result.hasTasks && result.tasks.length > 0) {
            // 有任务可处理
            const tasksCount = result.tasks.length;
            addLog('成功', `共获取到 ${tasksCount} 个待处理任务`);
            showNotification('任务', `获取到 ${tasksCount} 个任务`, 'success');

            // 创建用于处理任务的队列并开始处理
            let processedCount = 0;
            let errorCount = 0;

            // 创建处理任务的函数
            const processNextTask = async (index) => {
              if (index >= result.tasks.length) {
                // 所有任务处理完成
                addLog('信息', `所有任务处理完成，成功: ${processedCount}，失败: ${errorCount}`);
                showNotification('完成', `任务处理完成，成功: ${processedCount}，失败: ${errorCount}`, 'info');

                // 恢复按钮状态
                if (startReceiveTaskBtn) {
                  startReceiveTaskBtn.disabled = false;
                  startReceiveTaskBtn.textContent = '获取任务';
                }
                return;
              }

              const task = result.tasks[index];
              const taskId = task.id || `task_${Date.now()}_${index}`;
              const taskName = task.name || `任务 ${taskId}`;

              // 为每个任务创建独立的日志
              if (window.taskLogManager) {
                window.taskLogManager.createTaskLog(taskId, taskName);
              }

              addLog('信息', `处理任务 ${index + 1}/${tasksCount}: ID=${taskId}`);

              try {
                // 处理单个任务
                await processTask(task, token, taskId);
                processedCount++;
              } catch (error) {
                errorCount++;
                addLog('错误', `任务 ID=${taskId} 处理失败: ${error.message}`, taskId);
              }

              // 处理下一个任务
              await processNextTask(index + 1);
            };

            // 开始处理第一个任务
            await processNextTask(0);
          } else {
            // 没有任务可处理
            addLog('信息', '当前没有待处理任务');
            showNotification('提示', '当前没有待处理任务', 'info');

            // 恢复按钮状态
            if (startReceiveTaskBtn) {
              startReceiveTaskBtn.disabled = false;
              startReceiveTaskBtn.textContent = '获取任务';
            }
          }
        } else {
          throw new Error(result.error || '获取任务列表失败');
        }
      } catch (error) {
        console.error('获取任务失败:', error);
        addLog('错误', '获取任务失败: ' + error.message);
        showNotification('错误', '获取任务失败: ' + error.message, 'error');

        // 恢复按钮状态
        if (startReceiveTaskBtn) {
          startReceiveTaskBtn.disabled = false;
          startReceiveTaskBtn.textContent = '获取任务';
        }
      }
    })();
  }

  // 处理任务
  async function processTask(task, token, taskId) {
    try {
      // 打印任务详情，辅助调试
      console.log('处理任务:', task);
      addLog('信息', `开始处理任务 ID=${taskId}`, taskId);
      addLog('信息', `任务平台: ${task.platform || '未知'}`, taskId);

      // 记录更多任务详情
      if (task.name) {
        addLog('信息', `任务名称: ${task.name}`, taskId);
      }

      if (task.template_title) {
        addLog('信息', `任务标题模板: ${task.template_title}`, taskId);
      }

      if (task.browser_id) {
        addLog('信息', `浏览器ID: ${task.browser_id}`, taskId);
      }

      if (task.template_content) {
        addLog('信息', `内容模板: ${task.template_content.substring(0, 50)}...`, taskId);
        addLog('信息', `内容长度: ${task.template_content.length}字符`, taskId);
      }

      if (task.image_path) {
        addLog('信息', `图片路径: ${task.image_path}`, taskId);
      }

      // 构建基础任务数据
      const publishTaskData = {
        platform: task.platform || 'kuaishou',
        browserId: task.browser_id || '',
        rawTask: task  // 传递原始任务数据
      };

      addLog('信息', `准备发布到平台: ${publishTaskData.platform}`, taskId);

      // 调用主进程中的publishContent方法
      addLog('信息', '调用主进程发布内容...', taskId);

      // 设置事件监听器来接收实时日志
      const logHandler = (event, logData) => {
        addLog(logData.level, logData.message, taskId);
      };

      // 注册事件监听器
      window.electron.on('auto-publish:log', logHandler);

      try {
        const result = await window.electron.invoke('auto-publish:publish-content', publishTaskData);

        if (result.success) {
          addLog('成功', `任务内容发布成功: ${result.message || ''}`, taskId);

          // 标记任务完成
          if (window.taskLogManager) {
            const taskLog = window.taskLogManager.taskLogs.get(taskId);
            if (taskLog) {
              taskLog.status = 'completed';
            }
          }
        } else {
          throw new Error(`发布内容失败: ${result.error || '未知错误'}`);
        }
      } finally {
        // 清理事件监听器
        if (window.electron.off) {
          window.electron.off('auto-publish:log', logHandler);
        } else if (window.electron.removeEventListener) {
          window.electron.removeEventListener('auto-publish:log', logHandler);
        } else {
          // 如果都不支持，至少记录一个警告
          console.warn('无法清理事件监听器：未找到合适的清理方法');
        }
      }

      addLog('成功', `任务 ID=${taskId} 处理完成`, taskId);
      showNotification('成功', '任务处理完成', 'success');
    } catch (error) {
      console.error('处理任务失败:', error);
      addLog('错误', `处理任务 ID=${taskId} 失败: ${error.message}`, taskId);

      // 标记任务错误
      if (window.taskLogManager) {
        const taskLog = window.taskLogManager.taskLogs.get(taskId);
        if (taskLog) {
          taskLog.status = 'error';
        }
      }

      showNotification('错误', '任务处理失败: ' + error.message, 'error');
      throw error;
    }
  }
})();