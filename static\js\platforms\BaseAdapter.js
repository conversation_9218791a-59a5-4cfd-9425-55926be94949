/**
 * 基础平台适配器
 * 提供所有平台共用的基础功能
 */
const BrowserManager = require('../core/BrowserManager');
const AutoPublishUtils = require('../core/AutoPublishUtils');

class BaseAdapter {
  /**
   * 构造函数
   * @param {Object} plugin 插件实例
   */
  constructor(plugin) {
    this.plugin = plugin;
    this.logger = plugin.logger;
    this.sessions = new Map();  // 使用Map存储多个会话
    this.configs = new Map();   // 使用Map存储多个配置
    this.platform = null;       // 子类需要设置平台名称
  }

  /**
   * 发送日志到主进程
   * @param {string} level 日志级别
   * @param {string} message 日志消息
   * @param {string} [accountId] 账号ID
   */
  sendLog(level, message, accountId = null) {
    const accountInfo = accountId ? `[账号: ${accountId}] ` : '';
    const fullMessage = `${accountInfo}${message}`;

    // 发送日志到主进程
    if (this.plugin?.context?.mainWindow) {
      this.plugin.context.mainWindow.webContents.send('auto-publish:log', {
        level,
        message: fullMessage
      });
    }
    
    // 使用安全的日志记录方式
    if (this.logger) {
      switch (level.toLowerCase()) {
        case 'error':
          if (typeof this.logger.error === 'function') {
            this.logger.error(fullMessage);
          } else if (typeof this.logger.log === 'function') {
            this.logger.log(`[ERROR] ${fullMessage}`);
          }
          break;
        case 'warn':
          if (typeof this.logger.warn === 'function') {
            this.logger.warn(fullMessage);
          } else if (typeof this.logger.log === 'function') {
            this.logger.log(`[WARN] ${fullMessage}`);
          }
          break;
        case 'info':
          if (typeof this.logger.info === 'function') {
            this.logger.info(fullMessage);
          } else if (typeof this.logger.log === 'function') {
            this.logger.log(`[INFO] ${fullMessage}`);
          }
          break;
        case 'success':
          if (typeof this.logger.success === 'function') {
            this.logger.success(fullMessage);
          } else if (typeof this.logger.log === 'function') {
            this.logger.log(`[SUCCESS] ${fullMessage}`);
          }
          break;
        default:
          if (typeof this.logger.log === 'function') {
            this.logger.log(`[${level.toUpperCase()}] ${fullMessage}`);
          }
      }
    }
    
    // 如果logger不可用，至少输出到控制台
    if (!this.logger) {
      console.log(`[${level.toUpperCase()}] ${fullMessage}`);
    }
  }

  /**
   * 更新配置
   * @param {Object} rawTask API返回的原始任务数据
   * @param {string} accountId 账号ID
   * @returns {boolean} 更新是否成功
   */
  updateConfig(rawTask, accountId) {
    if (!accountId) {
      this.sendLog('错误', '缺少账号ID');
      return false;
    }

    // 检查任务数据是否有效
    if (!rawTask) {
      this.sendLog('错误', '无效的任务数据: ' + JSON.stringify(rawTask), accountId);
      return false;
    }

    try {
      // 构建基础配置
      const config = {
        // 任务基本信息
        id: rawTask.id,                    // 任务ID
        name: rawTask.name,                // 任务名称
        platform: rawTask.platform,        // 平台名称
        browser_id: rawTask.browser_id,    // 浏览器ID
        account_id: accountId,             // 账号ID
        
        // 发布控制参数
        count: rawTask.count,              // 发布次数
        delay: rawTask.delay,              // 操作延迟时间（秒）
        
        // 图片相关配置
        image_path: rawTask.image_path || rawTask.imagePath,  // 图片路径（支持两种key）
        img_random: rawTask.img_random,    // 是否随机选择图片
        min_images: rawTask.min_images || 3,    // 最少图片数量
        max_images: rawTask.max_images || 5,    // 最多图片数量
        
        // 时间控制参数
        schedule_time: rawTask.schedule_time,  // 初始时间
        max_minute: rawTask.max_minute,    // 最大分钟数
        min_minute: rawTask.min_minute,    // 最小分钟数
        
        // 地区相关配置
        region_ids: rawTask.region_ids,    // 地区ID列表
        region_names: rawTask.region_names,// 地区名称映射
        
        // 内容模板
        template_content: rawTask.template_content || rawTask.content,  // 内容模板（支持两种key）
        template_title: rawTask.template_title || rawTask.title,        // 标题模板（支持两种key）
        title_publish: rawTask.title_publish,  // 发布标题
        
        // 时间跳过设置
        skip_start_time: rawTask.skip_start_time,  // 跳过开始时间
        skip_end_time: rawTask.skip_end_time,      // 跳过结束时间
      };

      // 存储配置
      this.configs.set(accountId, config);

      // 输出配置更新日志（只显示关键信息）
      this.sendLog('信息', '配置已更新: ' + JSON.stringify({
        id: config.id,
        name: config.name,
        platform: config.platform,
        count: config.count,
        delay: config.delay,
        imagePath: config.image_path,
        templateContent: config.template_content?.substring(0, 50) + '...',  // 只显示前50个字符
        templateTitle: config.template_title,
        minImages: config.min_images,
        maxImages: config.max_images
      }), accountId);

      return true;
    } catch (error) {
      this.sendLog('错误', '更新配置失败: ' + error.message, accountId);
      return false;
    }
  }

  /**
   * 发布内容
   * @param {Object} content 内容数据
   * @param {Object} context 执行上下文
   * @returns {Promise<Object>} 发布结果
   */
  async publishContent(content, context) {
    const { logger } = context || { logger: this.logger };
    const accountId = content.accountId || content.browserId;
    
    // 基础验证
    if (!content.browserId || !content.rawTask) {
      return { success: false, error: '缺少必要参数' };
    }

    // 更新配置
    if (!this.updateConfig(content.rawTask, accountId)) {
      return { success: false, error: '任务数据无效' };
    }

    // 获取插件
    const bitBrowserPlugin = this.plugin.context?.getPlugin('bit-browser-plugin');
    if (!bitBrowserPlugin) {
      return { success: false, error: 'BitBrowser插件未找到' };
    }

    // 发布流程
    for (let retryCount = 0; retryCount <= 1; retryCount++) {
      try {
        // 连接浏览器
        const session = await BrowserManager.openAndConnect(content.browserId, bitBrowserPlugin, logger);
        this.sessions.set(accountId, session);
        
        // 执行发布
        const result = await this.publishWorkflow(accountId);
        await this.cleanup(accountId);
        return result;
          
      } catch (error) {
        logger.error(`发布失败 (尝试 ${retryCount + 1}/2): ${error.message}`);
        await this.cleanup(accountId);
        
        if (retryCount < 1) {
          await new Promise(resolve => setTimeout(resolve, 3000));
          continue;
        }
        
        return { success: false, error: error.message, retryCount };
      }
    }
  }

  /**
   * 等待指定时间
   * @param {number} baseTime 基础等待时间（毫秒）
   * @param {string} accountId 账号ID
   */
  async wait(baseTime = 1000, accountId) {
    const config = this.configs.get(accountId);
    const delay = config?.delay || 1;
    await new Promise(resolve => setTimeout(resolve, baseTime * delay));
  }

  /**
   * 清理资源
   * @param {string} accountId 账号ID
   */
  async cleanup(accountId) {
    this.sendLog('信息', '清理资源', accountId);
    const session = this.sessions.get(accountId);
    if (session) {
      await BrowserManager.close(session, this.logger);
      this.sessions.delete(accountId);
    }
  }

  /**
   * 生成随机发布时间
   * @param {string} [lastPublishTime] 上一次的发布时间
   * @param {string} accountId 账号ID
   * @returns {string} 格式化的时间字符串
   */
  generatePublishTime(lastPublishTime = null, accountId) {
    const config = this.configs.get(accountId);
    if (!config) {
      throw new Error(`未找到账号 ${accountId} 的配置`);
    }

    // 如果有上一次的发布时间，使用它作为基准时间
    const baseTime = lastPublishTime || config.schedule_time;
    
    // 生成随机时间
    const publishTime = AutoPublishUtils.generateRandomTime({
      startTime: config.skip_start_time,
      endTime: config.skip_end_time,
      timeMin: config.min_minute,
      timeMax: config.max_minute,
      scheduleTime: baseTime,  // 使用基准时间
      platform: this.platform  // 使用平台特定的时间格式
    });
    
    this.sendLog('信息', `设置定时发布时间: ${publishTime}`, accountId);
    return publishTime;
  }

  /**
   * 生成发布内容
   * @param {string} accountId 账号ID
   * @returns {Array} 生成的内容列表
   */
  generatePublishContents(accountId) {
    const config = this.configs.get(accountId);
    if (!config) {
      throw new Error(`未找到账号 ${accountId} 的配置`);
    }

    const count = config.count || 1;
    
    // 生成所有内容
    const publishContents = AutoPublishUtils.generatePublishContent({
      template_title: config.template_title,
      template_content: config.template_content,
      region_names: config.region_names,
      region_ids: config.region_ids,
      count
    }, this.logger);
    
    this.sendLog('信息', `已生成 ${publishContents.length} 条内容，使用地区列表: ${config.region_ids.join(', ')}`, accountId);
    
    return publishContents;
  }

  /**
   * 获取图片路径列表
   * @param {string} accountId 账号ID
   * @returns {Array} 图片路径列表
   */
  getImagePaths(accountId) {
    const config = this.configs.get(accountId);
    if (!config) {
      throw new Error(`未找到账号 ${accountId} 的配置`);
    }

    const imagePaths = AutoPublishUtils.getImagePaths(
      { 
        imagePath: config.image_path,
        minImages: config.min_images,
        maxImages: config.max_images,
        randomImages: config.img_random
      },
      this.logger
    );

    if (imagePaths.length > 0) {
      this.sendLog('信息', `准备上传 ${imagePaths.length} 张图片`, accountId);
    }

    return imagePaths;
  }

  /**
   * 格式化时间（子类可以覆盖此方法以提供平台特定的时间格式）
   * @param {Date} date 日期对象
   * @returns {string} 格式化后的时间字符串
   */
  formatTime(date) {
    // 默认格式：YYYY-MM-DD HH:mm:ss
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).replace(/\//g, '-');
  }

  /**
   * 发布工作流（需要子类实现）
   * @param {string} accountId 账号ID
   * @returns {Promise<Object>} 发布结果
   */
  async publishWorkflow(accountId) {
    throw new Error('子类必须实现 publishWorkflow 方法');
  }
}

module.exports = BaseAdapter; 