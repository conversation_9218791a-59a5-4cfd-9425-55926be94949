<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>任务日志系统测试</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    
    .test-container {
      max-width: 800px;
      margin: 0 auto;
    }
    
    .test-buttons {
      margin-bottom: 20px;
    }
    
    .test-buttons button {
      margin: 5px;
      padding: 8px 16px;
      border: 1px solid #ddd;
      background: #f8f9fa;
      cursor: pointer;
      border-radius: 4px;
    }
    
    .test-buttons button:hover {
      background: #e9ecef;
    }
    
    /* 引入任务日志样式 */
    .auto-publish-task-logs-container {
      height: 400px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      background-color: #f8f9fa;
      display: flex;
      flex-direction: column;
    }
    
    .auto-publish-task-tabs {
      display: flex;
      border-bottom: 1px solid #e0e0e0;
      background-color: #fff;
      overflow-x: auto;
      flex-shrink: 0;
    }
    
    .auto-publish-task-tab {
      padding: 8px 16px;
      border: none;
      background: none;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      white-space: nowrap;
      font-size: 12px;
      color: #666;
      position: relative;
    }
    
    .auto-publish-task-tab.active {
      color: #007bff;
      border-bottom-color: #007bff;
      background-color: #f8f9fa;
    }
    
    .auto-publish-task-tab:hover {
      background-color: #f8f9fa;
    }
    
    .auto-publish-task-tab-close {
      margin-left: 8px;
      color: #999;
      font-weight: bold;
    }
    
    .auto-publish-task-tab-close:hover {
      color: #dc3545;
    }
    
    .auto-publish-task-log-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    
    .auto-publish-task-log-pane {
      display: none;
      flex: 1;
      flex-direction: column;
      overflow: hidden;
    }
    
    .auto-publish-task-log-pane.active {
      display: flex;
    }
    
    .auto-publish-task-log-output {
      flex: 1;
      overflow-y: auto;
      padding: 10px;
      margin: 0;
      white-space: pre-wrap;
      word-break: break-word;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
    }
    
    .auto-publish-task-log-actions {
      padding: 8px 10px;
      border-top: 1px solid #e0e0e0;
      background-color: #fff;
      display: flex;
      gap: 8px;
      flex-shrink: 0;
    }
    
    .auto-publish-task-log-actions button {
      padding: 4px 8px;
      font-size: 12px;
      border: 1px solid #ddd;
      background: #fff;
      border-radius: 3px;
      cursor: pointer;
    }
    
    .auto-publish-task-log-actions button:hover {
      background-color: #f8f9fa;
    }
    
    .auto-publish-task-log-actions .refresh-btn {
      color: #28a745;
      border-color: #28a745;
    }
    
    .auto-publish-task-log-actions .stop-btn {
      color: #dc3545;
      border-color: #dc3545;
    }
    
    .auto-publish-task-log-actions .history-btn {
      color: #17a2b8;
      border-color: #17a2b8;
    }
    
    .auto-publish-task-log-actions .close-btn {
      color: #6c757d;
      border-color: #6c757d;
    }
    
    .log-entry {
      margin-bottom: 2px;
      padding: 2px 0;
    }
    
    .log-level-info { color: #17a2b8; }
    .log-level-success { color: #28a745; }
    .log-level-warning { color: #ffc107; }
    .log-level-error { color: #dc3545; }
    
    .auto-publish-empty-message {
      text-align: center;
      color: #999;
      padding: 20px;
      font-style: italic;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>任务日志系统测试</h1>
    
    <div class="test-buttons">
      <button onclick="createTestTask()">创建测试任务</button>
      <button onclick="addTestLog()">添加测试日志</button>
      <button onclick="addGlobalLog()">添加全局日志</button>
      <button onclick="clearAllLogs()">清空所有日志</button>
    </div>
    
    <div class="auto-publish-task-logs-container" id="task-logs-container">
      <!-- 任务标签页 -->
      <div class="auto-publish-task-tabs" id="task-tabs">
        <button class="auto-publish-task-tab active" data-task-id="global">
          全局日志
          <span class="auto-publish-task-tab-close" onclick="event.stopPropagation(); closeTaskLog('global')">×</span>
        </button>
      </div>
      
      <!-- 任务日志内容 -->
      <div class="auto-publish-task-log-content" id="task-log-content">
        <!-- 全局日志面板 -->
        <div class="auto-publish-task-log-pane active" id="task-log-global">
          <pre class="auto-publish-task-log-output" id="global-log-output"></pre>
          <div class="auto-publish-task-log-actions">
            <button class="refresh-btn" onclick="refreshGlobalLogs()">刷新</button>
            <button class="close-btn" onclick="clearGlobalLogs()">清空</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 模拟任务日志管理器
    window.taskLogManager = {
      taskLogs: new Map(),
      activeTaskId: 'global',
      
      createTaskLog: function(taskId, taskName) {
        if (this.taskLogs.has(taskId)) {
          return;
        }
        
        const taskLog = {
          id: taskId,
          name: taskName || `任务 ${taskId}`,
          logs: [],
          status: 'running',
          startTime: new Date()
        };
        
        this.taskLogs.set(taskId, taskLog);
        this.createTaskTab(taskId, taskLog.name);
        this.switchToTask(taskId);
      },
      
      createTaskTab: function(taskId, taskName) {
        const tabsContainer = document.getElementById('task-tabs');
        if (!tabsContainer) return;
        
        const tab = document.createElement('button');
        tab.className = 'auto-publish-task-tab';
        tab.setAttribute('data-task-id', taskId);
        tab.innerHTML = `
          ${taskName}
          <span class="auto-publish-task-tab-close" onclick="event.stopPropagation(); closeTaskLog('${taskId}')">×</span>
        `;
        tab.onclick = () => this.switchToTask(taskId);
        
        tabsContainer.appendChild(tab);
        this.createTaskLogPane(taskId);
      },
      
      createTaskLogPane: function(taskId) {
        const contentContainer = document.getElementById('task-log-content');
        if (!contentContainer) return;
        
        const pane = document.createElement('div');
        pane.className = 'auto-publish-task-log-pane';
        pane.id = `task-log-${taskId}`;
        pane.innerHTML = `
          <pre class="auto-publish-task-log-output" id="${taskId}-log-output"></pre>
          <div class="auto-publish-task-log-actions">
            <button class="refresh-btn" onclick="refreshTaskLog('${taskId}')">重新获取</button>
            <button class="stop-btn" onclick="stopTask('${taskId}')">停止</button>
            <button class="history-btn" onclick="showTaskHistory('${taskId}')">历史日志</button>
            <button class="close-btn" onclick="closeTaskLog('${taskId}')">关闭</button>
          </div>
        `;
        
        contentContainer.appendChild(pane);
      },
      
      switchToTask: function(taskId) {
        document.querySelectorAll('.auto-publish-task-tab').forEach(tab => {
          tab.classList.toggle('active', tab.getAttribute('data-task-id') === taskId);
        });
        
        document.querySelectorAll('.auto-publish-task-log-pane').forEach(pane => {
          pane.classList.toggle('active', pane.id === `task-log-${taskId}`);
        });
        
        this.activeTaskId = taskId;
        this.scrollToBottom(taskId);
      },
      
      addLog: function(taskId, level, message) {
        const taskLog = this.taskLogs.get(taskId);
        if (!taskLog) {
          this.addGlobalLog(level, message);
          return;
        }
        
        const log = {
          time: new Date(),
          level,
          message
        };
        
        taskLog.logs.push(log);
        this.updateTaskLogDisplay(taskId);
      },
      
      addGlobalLog: function(level, message) {
        if (!window.autoPublish) {
          window.autoPublish = { logs: [] };
        }
        
        const log = {
          time: new Date(),
          level,
          message
        };
        
        window.autoPublish.logs.push(log);
        this.updateGlobalLogDisplay();
      },
      
      updateTaskLogDisplay: function(taskId) {
        const taskLog = this.taskLogs.get(taskId);
        if (!taskLog) return;
        
        const outputElement = document.getElementById(`${taskId}-log-output`);
        if (!outputElement) return;
        
        if (taskLog.logs.length === 0) {
          outputElement.innerHTML = '<div class="auto-publish-empty-message">暂无日志</div>';
          return;
        }
        
        outputElement.innerHTML = taskLog.logs.map(log => {
          const timeStr = log.time.toLocaleString();
          const levelClass = `log-level-${log.level.toLowerCase()}`;
          return `<div class="log-entry ${levelClass}">[${timeStr}] [${log.level}] ${log.message}</div>`;
        }).join('\n');
        
        this.scrollToBottom(taskId);
      },
      
      updateGlobalLogDisplay: function() {
        const outputElement = document.getElementById('global-log-output');
        if (!outputElement) return;
        
        if (!window.autoPublish || !window.autoPublish.logs || window.autoPublish.logs.length === 0) {
          outputElement.innerHTML = '<div class="auto-publish-empty-message">暂无日志</div>';
          return;
        }
        
        outputElement.innerHTML = window.autoPublish.logs.map(log => {
          const timeStr = log.time.toLocaleString();
          const levelClass = `log-level-${log.level.toLowerCase()}`;
          return `<div class="log-entry ${levelClass}">[${timeStr}] [${log.level}] ${log.message}</div>`;
        }).join('\n');
        
        this.scrollToBottom('global');
      },
      
      scrollToBottom: function(taskId) {
        const outputElement = document.getElementById(`${taskId}-log-output`);
        if (outputElement) {
          outputElement.scrollTop = outputElement.scrollHeight;
        }
      },
      
      closeTask: function(taskId) {
        if (taskId === 'global') {
          this.clearGlobalLogs();
          return;
        }
        
        this.taskLogs.delete(taskId);
        
        const tab = document.querySelector(`[data-task-id="${taskId}"]`);
        if (tab) {
          tab.remove();
        }
        
        const pane = document.getElementById(`task-log-${taskId}`);
        if (pane) {
          pane.remove();
        }
        
        if (this.activeTaskId === taskId) {
          this.switchToTask('global');
        }
      },
      
      clearGlobalLogs: function() {
        if (window.autoPublish) {
          window.autoPublish.logs = [];
        }
        this.updateGlobalLogDisplay();
      },
      
      clearAllLogs: function() {
        this.taskLogs.clear();
        this.clearGlobalLogs();
        
        document.querySelectorAll('.auto-publish-task-tab:not([data-task-id="global"])').forEach(tab => {
          tab.remove();
        });
        document.querySelectorAll('.auto-publish-task-log-pane:not(#task-log-global)').forEach(pane => {
          pane.remove();
        });
        
        this.switchToTask('global');
      }
    };
    
    // 全局函数
    window.closeTaskLog = function(taskId) {
      window.taskLogManager.closeTask(taskId);
    };
    
    window.refreshTaskLog = function(taskId) {
      window.taskLogManager.addLog(taskId, '信息', '重新获取任务...');
    };
    
    window.stopTask = function(taskId) {
      window.taskLogManager.addLog(taskId, '警告', '任务已停止');
      const taskLog = window.taskLogManager.taskLogs.get(taskId);
      if (taskLog) {
        taskLog.status = 'stopped';
      }
    };
    
    window.showTaskHistory = function(taskId) {
      window.taskLogManager.addLog(taskId, '信息', '显示历史日志...');
    };
    
    window.refreshGlobalLogs = function() {
      window.taskLogManager.updateGlobalLogDisplay();
    };
    
    window.clearGlobalLogs = function() {
      window.taskLogManager.clearGlobalLogs();
    };
    
    // 测试函数
    let taskCounter = 1;
    
    function createTestTask() {
      const taskId = `task_${taskCounter++}`;
      const taskName = `测试任务 ${taskCounter - 1}`;
      window.taskLogManager.createTaskLog(taskId, taskName);
      window.taskLogManager.addLog(taskId, '信息', '任务已创建');
    }
    
    function addTestLog() {
      const activeTaskId = window.taskLogManager.activeTaskId;
      const levels = ['信息', '成功', '警告', '错误'];
      const level = levels[Math.floor(Math.random() * levels.length)];
      const message = `这是一条测试日志消息 - ${new Date().toLocaleTimeString()}`;
      
      if (activeTaskId === 'global') {
        window.taskLogManager.addGlobalLog(level, message);
      } else {
        window.taskLogManager.addLog(activeTaskId, level, message);
      }
    }
    
    function addGlobalLog() {
      const levels = ['信息', '成功', '警告', '错误'];
      const level = levels[Math.floor(Math.random() * levels.length)];
      const message = `全局日志消息 - ${new Date().toLocaleTimeString()}`;
      window.taskLogManager.addGlobalLog(level, message);
    }
    
    function clearAllLogs() {
      window.taskLogManager.clearAllLogs();
    }
    
    // 初始化
    window.autoPublish = { logs: [] };
    window.taskLogManager.updateGlobalLogDisplay();
    window.taskLogManager.addGlobalLog('信息', '任务日志系统已初始化');
  </script>
</body>
</html>
