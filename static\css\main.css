/* 基础样式 */
.auto-publish-container {
  /* 自定义变量 */
  --primary-color: #4CAF50;  /* 改为绿色，与主框架颜色一致 */
  --primary-dark: #388E3C;
  --secondary-color: #f8f9fa;
  --text-color: #333;
  --light-text: #666;
  --border-color: #ddd;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --error-color: #dc3545;
  --gray-bg: #f5f5f5;
  --dark-gray: #888;
  --white: #fff;
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --radius: 4px;
  --space: 15px;
  
  /* 基础样式 */
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--white);
  overflow: hidden;
  
  /* 布局 */
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 100vh;
}

/* 重置插件内部元素的样式 */
.auto-publish-container * {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 布局 */
.auto-publish-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 100vh;
}

.auto-publish-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space);
  background-color: var(--primary-color);
  color: var(--white);
  box-shadow: var(--shadow);
  z-index: 10;
}

.auto-publish-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 标题栏 */
.auto-publish-header h1 {
  font-size: 18px;
  font-weight: 500;
}

/* 选项卡 */
.auto-publish-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--primary-color);
  padding: 0 10px;
}

.auto-publish-tab-btn {
  padding: 12px var(--space);
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.2s;
}

.auto-publish-tab-btn:hover {
  color: var(--white);
  background-color: rgba(255, 255, 255, 0.1);
}

.auto-publish-tab-btn.active {
  color: var(--white);
  border-bottom-color: var(--white);
}

.auto-publish-tab-content {
  flex: 1;
  overflow: auto;
  padding: var(--space);
  background-color: var(--white);
}

.auto-publish-tab-pane {
  display: none;
  animation: auto-publish-fade-in 0.3s ease;
}

.auto-publish-tab-pane.active {
  display: block;
}

/* 表单样式 */
.auto-publish-form {
  max-width: 800px;
  margin: 0 auto;
  background-color: var(--white);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.auto-publish-form-group {
  margin-bottom: var(--space);
}

.auto-publish-form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
}

.auto-publish-input, .auto-publish-select, .auto-publish-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  font-family: inherit;
  font-size: 14px;
  transition: all 0.2s;
}

.auto-publish-input:focus, .auto-publish-select:focus, .auto-publish-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.auto-publish-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.auto-publish-primary-btn {
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.auto-publish-primary-btn:hover {
  background-color: var(--primary-dark);
}

.auto-publish-secondary-btn {
  padding: 10px 20px;
  background-color: var(--secondary-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.auto-publish-secondary-btn:hover {
  background-color: var(--gray-bg);
}

/* 图片上传 */
.auto-publish-image-uploader {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.auto-publish-image-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
}

.auto-publish-image-status {
  color: var(--light-text);
  font-size: 13px;
}

.auto-publish-image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.auto-publish-image-preview {
  margin-bottom: 10px;
}

.auto-publish-image-item {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  aspect-ratio: 1/1;
  transition: transform 0.2s;
}

.auto-publish-image-item:hover {
  transform: translateY(-3px);
}

.auto-publish-image-preview-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.auto-publish-image-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 4px;
}

.auto-publish-image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 8px;
  background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
  color: #fff;
  opacity: 0;
  transition: opacity 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.auto-publish-image-item:hover .auto-publish-image-overlay {
  opacity: 1;
}

.auto-publish-image-name {
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 75%;
}

.auto-publish-image-remove {
  background: rgba(220, 53, 69, 0.8);
  color: white;
  border: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  padding: 0;
  transition: background-color 0.2s;
}

.auto-publish-image-remove:hover {
  background: rgba(220, 53, 69, 1);
}

.auto-publish-no-images {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: var(--light-text);
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  font-size: 14px;
  grid-column: 1 / -1;
}

/* 话题标签 */
.auto-publish-topics-container {
  margin-top: 10px;
}

.auto-publish-topics-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.auto-publish-topic-tag {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  border-radius: 20px;
  font-size: 13px;
  color: var(--primary-color);
  transition: all 0.2s;
}

.auto-publish-topic-tag:hover {
  background-color: rgba(76, 175, 80, 0.2);
}

.auto-publish-topic-tag .auto-publish-remove-btn {
  margin-left: 8px;
  cursor: pointer;
  font-size: 14px;
  color: var(--primary-color);
  opacity: 0.7;
}

.auto-publish-topic-tag .auto-publish-remove-btn:hover {
  opacity: 1;
}

.auto-publish-topics-input-container {
  display: flex;
  gap: 10px;
}

.auto-publish-topics-input-container input {
  flex: 1;
}

/* 任务列表 */
.auto-publish-panel-heading {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.auto-publish-panel-heading h2 {
  font-size: 18px;
  font-weight: 500;
  color: var(--primary-color);
}

.auto-publish-tasks-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.auto-publish-task-item {
  padding: 15px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--white);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s, box-shadow 0.2s;
}

.auto-publish-task-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.auto-publish-task-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
}

.auto-publish-task-platform {
  font-weight: 600;
  color: var(--primary-color);
}

.auto-publish-task-status {
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  background-color: var(--gray-bg);
}

.auto-publish-task-content {
  margin-bottom: 12px;
  color: var(--text-color);
  font-size: 14px;
  line-height: 1.6;
}

.auto-publish-task-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid var(--border-color);
}

.auto-publish-task-time {
  color: var(--dark-gray);
}

.auto-publish-task-actions {
  display: flex;
  gap: 8px;
}

.auto-publish-task-actions .auto-publish-btn {
  padding: 5px 10px;
  font-size: 12px;
}

/* 任务状态样式 */
.auto-publish-status-pending .auto-publish-task-status {
  background-color: var(--secondary-color);
  color: var(--dark-gray);
}

.auto-publish-status-running .auto-publish-task-status {
  background-color: var(--primary-color);
  color: var(--white);
}

.auto-publish-status-success .auto-publish-task-status {
  background-color: var(--success-color);
  color: var(--white);
}

.auto-publish-status-failed .auto-publish-task-status {
  background-color: var(--error-color);
  color: var(--white);
}

.auto-publish-status-canceled .auto-publish-task-status {
  background-color: var(--dark-gray);
  color: var(--white);
}

/* 空状态提示 */
.auto-publish-empty-message {
  text-align: center;
  padding: 40px 0;
  color: var(--dark-gray);
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  border: 1px dashed var(--border-color);
}

/* 通知样式 */
.auto-publish-notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 300px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.auto-publish-notification {
  display: flex;
  padding: 15px;
  border-radius: 8px;
  background-color: var(--white);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: auto-publish-slide-in 0.3s ease;
}

.auto-publish-notification-icon {
  margin-right: 10px;
  font-size: 18px;
}

.auto-publish-notification-content {
  flex: 1;
  font-size: 14px;
}

.auto-publish-notification-close {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: var(--dark-gray);
  opacity: 0.7;
  transition: opacity 0.2s;
}

.auto-publish-notification-close:hover {
  opacity: 1;
}

.auto-publish-notification.info {
  border-left: 4px solid var(--primary-color);
}

.auto-publish-notification.success {
  border-left: 4px solid var(--success-color);
}

.auto-publish-notification.warning {
  border-left: 4px solid var(--warning-color);
}

.auto-publish-notification.error {
  border-left: 4px solid var(--error-color);
}

.auto-publish-notification-hiding {
  animation: auto-publish-slide-out 0.3s ease forwards;
}

/* 加载状态 */
.auto-publish-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.auto-publish-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.auto-publish-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--white);
  animation: auto-publish-spin 1s linear infinite;
  margin-bottom: 15px;
}

.auto-publish-loading-message {
  color: var(--white);
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 对话框 */
.auto-publish-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  /* 移除模糊效果 */
  /* backdrop-filter: blur(2px); */
}

.auto-publish-dialog {
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  background-color: white; /* 确保是纯白背景 */
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  /* 移除动画效果 */
  /* animation: auto-publish-dialog-in 0.3s ease; */
}

.auto-publish-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--primary-color);
  color: white;
}

.auto-publish-dialog-header h3 {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

.auto-publish-dialog-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.2s;
}

.auto-publish-dialog-close:hover {
  color: white;
}

.auto-publish-dialog-content {
  padding: 20px;
  overflow: auto;
}

/* 新增动画 */
@keyframes auto-publish-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes auto-publish-dialog-in {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 任务详情 */
.auto-publish-detail-item {
  margin-bottom: 15px;
  display: flex;
}

.auto-publish-detail-label {
  width: 100px;
  font-weight: 600;
  color: var(--dark-gray);
}

.auto-publish-detail-value {
  flex: 1;
}

.auto-publish-detail-value.content {
  margin-top: 10px;
  padding: 15px;
  background-color: var(--gray-bg);
  border-radius: 8px;
  white-space: pre-wrap;
  font-family: monospace;
}

/* 日志项 */
.auto-publish-task-logs {
  margin-top: 20px;
  border-top: 1px solid var(--border-color);
  padding-top: 15px;
}

.auto-publish-log-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
  font-size: 13px;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.auto-publish-log-time {
  width: 150px;
  color: var(--dark-gray);
  font-family: monospace;
}

.auto-publish-log-level {
  width: 50px;
  font-weight: 600;
  margin-right: 10px;
  text-transform: uppercase;
}

.auto-publish-log-level.info {
  color: var(--primary-color);
}

.auto-publish-log-level.warn {
  color: var(--warning-color);
}

.auto-publish-log-level.error {
  color: var(--error-color);
}

.auto-publish-log-level.debug {
  color: var(--dark-gray);
}

.auto-publish-log-message {
  flex: 1;
}

/* 错误信息 */
.auto-publish-task-error {
  margin-top: 15px;
  padding: 15px;
  background-color: rgba(220, 53, 69, 0.1);
  border-left: 4px solid var(--error-color);
  color: var(--error-color);
  white-space: pre-wrap;
  border-radius: 4px;
  font-family: monospace;
  font-size: 13px;
}

/* 动画 */
@keyframes auto-publish-spin {
  to { transform: rotate(360deg); }
}

@keyframes auto-publish-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes auto-publish-slide-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .auto-publish-tab-content {
    padding: 10px;
  }
  
  .auto-publish-form-actions {
    flex-direction: column;
  }
  
  .auto-publish-detail-item {
    flex-direction: column;
  }
  
  .auto-publish-detail-label {
    width: 100%;
    margin-bottom: 5px;
  }
}

/* 账号列表 */
.auto-publish-accounts-list {
  display: none; /* 隐藏账号列表，而非完全删除，保持CSS结构 */
}

.auto-publish-account-item {
  display: none; /* 隐藏账号项目 */
}

.auto-publish-account-info {
  display: none;
}

.auto-publish-account-name {
  display: none;
}

.auto-publish-account-platform {
  display: none;
}

.auto-publish-account-actions {
  display: none;
}

/* API面板相关样式 */
.auto-publish-api-info {
  padding: 20px;
}

.auto-publish-api-field {
  margin-bottom: 15px;
}

.auto-publish-api-field label {
  display: block;
  font-weight: bold;
  margin-bottom: 5px;
}

.auto-publish-api-value-container {
  display: flex;
  gap: 10px;
}

.auto-publish-api-value-container input {
  flex: 1;
}

.auto-publish-api-docs {
  margin-top: 30px;
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.auto-publish-api-docs h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 16px;
}

.auto-publish-code {
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  font-family: monospace;
  white-space: pre-wrap;
  font-size: 13px;
  overflow: auto;
  max-height: 300px;
}

/* BitBrowser面板相关样式 */
.auto-publish-browser-info {
  padding: 20px;
}

.auto-publish-info-card {
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.auto-publish-info-header {
  padding: 15px;
  background-color: #f8f8f8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ddd;
}

.auto-publish-status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.auto-publish-status-badge.connected {
  background-color: #4caf50;
  color: white;
}

.auto-publish-status-badge.disconnected {
  background-color: #f44336;
  color: white;
}

.auto-publish-info-body {
  padding: 15px;
}

.auto-publish-info-row {
  display: flex;
  margin-bottom: 10px;
}

.auto-publish-info-row label {
  font-weight: bold;
  width: 120px;
}

.auto-publish-info-footer {
  padding: 15px;
  background-color: #f8f8f8;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  border-top: 1px solid #ddd;
}

/* 日志面板相关样式 */
.auto-publish-logs-container {
  height: calc(100% - 60px);
  padding: 10px;
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin: 0 20px 20px 20px;
}

.auto-publish-logs-output {
  height: 100%;
  overflow: auto;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
  padding: 10px;
}

.log-entry {
  margin-bottom: 5px;
}

.log-level-信息 {
  color: #333;
}

.log-level-成功 {
  color: #4caf50;
}

.log-level-警告 {
  color: #ff9800;
}

.log-level-错误 {
  color: #f44336;
}

/* 调整测试表单的名称 */
#test-form.auto-publish-form {
  padding: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .auto-publish-api-value-container {
    flex-direction: column;
  }
  
  .auto-publish-info-row {
    flex-direction: column;
  }
  
  .auto-publish-info-row label {
    width: 100%;
    margin-bottom: 5px;
  }
} 