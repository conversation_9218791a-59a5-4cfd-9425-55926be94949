/**
 * 浏览器管理器
 * 处理浏览器打开、连接与管理的相关功能
 */
const AutoPublishUtils = require('./AutoPublishUtils');

class BrowserManager {
  /**
   * 打开并连接到BitBrowser浏览器
   * @param {string} browserId 浏览器ID
   * @param {Object} bitBrowserPlugin BitBrowser插件实例
   * @param {Object} logger 日志对象
   * @returns {Promise<Object>} 浏览器会话对象
   */
  static async openAndConnect(browserId, bitBrowserPlugin, logger) {
    if (!browserId) {
      throw new Error('必须提供浏览器ID');
    }
    
    if (!bitBrowserPlugin) {
      throw new Error('BitBrowser插件未找到');
    }
    
    logger.log(`使用浏览器ID: ${browserId}`);
    
    // 打开浏览器
    const browserData = await this.openBrowser(browserId, bitBrowserPlugin, logger);
    
    // 连接到浏览器
    const session = await this.connectToBrowser(browserData, logger);
    
    return session;
  }
  
  /**
   * 打开BitBrowser浏览器
   * @param {string} browserId 浏览器ID
   * @param {Object} bitBrowserPlugin BitBrowser插件实例
   * @param {Object} logger 日志对象
   * @returns {Promise<Object>} 浏览器数据
   * @private
   */
  static async openBrowser(browserId, bitBrowserPlugin, logger) {
    logger.log(`正在打开浏览器 ID: ${browserId}...`);
    
    try {
      // 检查浏览器是否已经打开
      try {
        await bitBrowserPlugin.getBrowserDetail(browserId);
        logger.log('浏览器已存在，获取详情成功');
      } catch (err) {
        logger.warn('获取浏览器详情失败，尝试重新打开:', err.message);
      }
      
      // 使用BitBrowser API打开浏览器
      const browserResult = await bitBrowserPlugin.openBrowser({
        id: browserId,
        args: [],
        loadExtensions: false,
        extractIp: false
      });
      
      if (!browserResult.success) {
        throw new Error(`打开浏览器失败: ${browserResult.error || '未知错误'}`);
      }
      
      logger.log('浏览器已成功打开，等待初始化...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      return browserResult.data;
    } catch (error) {
      logger.error(`打开浏览器失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 连接到浏览器
   * @param {Object} browserData 浏览器数据
   * @param {Object} logger 日志对象
   * @returns {Promise<Object>} 浏览器会话对象
   * @private
   */
  static async connectToBrowser(browserData, logger) {
    if (!browserData || !browserData.ws) {
      throw new Error('无效的浏览器数据或WebSocket地址');
    }
    
    const wsEndpoint = browserData.ws;
    logger.log(`正在连接到浏览器WebSocket: ${wsEndpoint}`);
    
    try {
      const { browser, context } = await AutoPublishUtils.connectToBrowser(wsEndpoint, logger);
      
      // 获取所有页面
      const pages = context.pages();
      logger.log(`现有页面数量: ${pages.length}`);
      
      return {
        browser,
        context,
        pages,
        wsEndpoint,
        id: browserData.id
      };
    } catch (error) {
      logger.error(`连接到浏览器失败: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * 根据URL前缀找到页面
   * @param {Object} session 浏览器会话
   * @param {string} urlPrefix URL前缀
   * @param {Object} logger 日志对象
   * @returns {Promise<Object>} 找到的页面和创建状态
   */
  static async findOrCreatePage(session, urlPrefix, defaultUrl, logger) {
    // 寻找匹配的页面
    let matchedPage = null;
    let isNewPage = false;
    
    for (const page of session.pages) {
      try {
        const url = page.url();
        logger.log(`检查页面URL: ${url}`);
        if (url.includes(urlPrefix)) {
          logger.log(`找到匹配的页面: ${url}`);
          matchedPage = page;
          break;
        }
      } catch (e) {
        logger.warn(`检查页面URL失败: ${e.message}`);
      }
    }
    
    // 如果没找到，创建新页面
    if (!matchedPage) {
      logger.log(`未找到匹配 ${urlPrefix} 的页面，创建新页面...`);
      matchedPage = await session.context.newPage();
      isNewPage = true;
      
      if (defaultUrl) {
        logger.log(`导航到默认URL: ${defaultUrl}`);
        await matchedPage.goto(defaultUrl, { timeout: 30000 });
      }
    }
    
    // 切换到该页面
    await matchedPage.bringToFront();
    logger.log('已切换到目标页面');
    
    // 等待页面加载
    await matchedPage.waitForLoadState('domcontentloaded');
    await matchedPage.waitForLoadState('networkidle');
    
    return { page: matchedPage, isNewPage };
  }
  
  /**
   * 安全关闭浏览器连接
   * @param {Object} session 浏览器会话
   * @param {Object} logger 日志对象
   */
  static async close(session, logger) {
    if (!session || !session.browser) {
      return;
    }
    
    try {
      await AutoPublishUtils.closeBrowserConnection(session.browser, logger);
      logger.log('浏览器会话已安全关闭');
    } catch (error) {
      logger.error(`关闭浏览器会话失败: ${error.message}`);
    }
  }
}

module.exports = BrowserManager; 