/**
 * API客户端 - 集中管理所有前端API调用
 */
(function() {
  // API基础URL - 集中配置，便于修改
  const API_BASE_URL = 'http://api.274500.xyz';
  
  // 导出API客户端对象到全局
  window.apiClient = {
    /**
     * 获取API令牌
     * @param {string} username 用户名
     * @param {string} password 密码
     * @returns {Promise<Object>} API令牌信息
     */
    getApiToken: async function(username, password) {
      try {
        const response = await fetch(`${API_BASE_URL}/api/token`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ username, password })
        });
        
        const responseData = await response.json();
        
        // 处理API返回格式
        if (responseData.code === 200 && responseData.data) {
          // 返回适配后的数据格式
          return {
            success: true,
            token: responseData.data.token,
            userId: responseData.data.user_id,
            username: responseData.data.username
          };
        } else {
          return {
            success: false,
            error: responseData.msg || '获取API令牌失败'
          };
        }
      } catch (error) {
        console.error('获取API令牌失败:', error);
        return { success: false, error: error.message };
      }
    },
    
    /**
     * 获取待处理任务
     * @param {string} token API令牌
     * @returns {Promise<Object>} 任务数据
     */
    getTask: async function(token) {
      try {
        const response = await fetch(`${API_BASE_URL}/api/get-task`, {
          method: 'GET',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });
        const responseData = await response.json();
        
        // 处理API返回格式
        if (responseData.code === 200) {
          // 如果有任务数据
          if (responseData.data && responseData.data.task) {
            return {
              success: true,
              hasTask: true,
              task: responseData.data.task
            };
          } else {
            // 没有待处理任务
            return {
              success: true,
              hasTask: false
            };
          }
        } else {
          return {
            success: false,
            error: responseData.msg || '获取任务失败'
          };
        }
      } catch (error) {
        console.error('获取任务失败:', error);
        return { success: false, error: error.message };
      }
    },
    
    /**
     * 获取所有待处理任务
     * @param {string} token API令牌
     * @returns {Promise<Object>} 所有任务数据
     */
    getAllTasks: async function(token) {
      try {
        const response = await fetch(`${API_BASE_URL}/api/get-task`, {
          method: 'GET',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });
        const responseData = await response.json();
        
        // 处理API返回格式
        if (responseData.code === 200) {
          // 如果有任务数据 - API返回的是单个任务而不是任务数组
          if (responseData.data && responseData.data.task) {
            return {
              success: true,
              hasTasks: true,
              tasks: [responseData.data.task] // 将单个任务放入数组中
            };
          } else {
            // 没有待处理任务
            return {
              success: true,
              hasTasks: false,
              tasks: []
            };
          }
        } else {
          return {
            success: false,
            error: responseData.msg || '获取任务列表失败'
          };
        }
      } catch (error) {
        console.error('获取任务列表失败:', error);
        return { success: false, error: error.message };
      }
    },
    
    /**
     * 发布内容
     * @param {Object} taskData 任务数据
     * @param {string} token API令牌
     * @returns {Promise<Object>} 发布结果
     */
    publishContent: async function(taskData, token) {
      try {
        // 添加调试日志
        console.log('发布内容数据:', taskData);
        
        const response = await fetch(`${API_BASE_URL}/api/task`, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(taskData)
        });
        const responseData = await response.json();
        
        // 添加调试日志
        console.log('发布内容响应:', responseData);
        
        // 处理API返回格式
        if (responseData.code === 200 && responseData.data) {
          return {
            success: true,
            taskId: responseData.data.task_id || responseData.data.taskId,
            message: responseData.msg || '任务提交成功'
          };
        } else {
          return {
            success: false,
            error: responseData.msg || '发布内容失败'
          };
        }
      } catch (error) {
        console.error('发布内容失败:', error);
        return { success: false, error: error.message };
      }
    }
  };
})(); 